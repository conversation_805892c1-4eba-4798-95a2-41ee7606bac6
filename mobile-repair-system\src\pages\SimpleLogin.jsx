import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Text<PERSON>ield,
  But<PERSON>,
  Typo<PERSON>,
  Al<PERSON>,
  Container
} from '@mui/material';
import { useAuth } from '../context/SimpleAuthContext';

const SimpleLogin = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const result = login(username, password);
    if (!result.success) {
      setError(result.message);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        direction: 'rtl'
      }}
    >
      <Container maxWidth="sm">
        <Card sx={{ borderRadius: 4, boxShadow: '0 20px 60px rgba(0,0,0,0.2)' }}>
          <CardContent sx={{ p: 6 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h3" component="h1" gutterBottom sx={{ color: '#1B5E20', fontWeight: 'bold' }}>
                🇮🇶 مركز الرافدين
              </Typography>
              <Typography variant="h5" color="text.secondary">
                نظام إدارة صيانة الهواتف المحمولة
              </Typography>
            </Box>

            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="اسم المستخدم"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                margin="normal"
                required
                sx={{ mb: 2 }}
                InputProps={{
                  style: { textAlign: 'right', direction: 'rtl' }
                }}
              />

              <TextField
                fullWidth
                label="كلمة المرور"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                margin="normal"
                required
                sx={{ mb: 3 }}
                InputProps={{
                  style: { textAlign: 'right', direction: 'rtl' }
                }}
              />

              {error && (
                <Alert severity="error" sx={{ mb: 2, textAlign: 'right' }}>
                  {error}
                </Alert>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  py: 2,
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #0D4E14 0%, #2E7D32 100%)',
                  }
                }}
              >
                دخول إلى النظام
              </Button>
            </form>

            <Box sx={{ mt: 4, p: 2, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                <strong>معلومات تجريبية:</strong><br />
                اسم المستخدم: abd<br />
                كلمة المرور: ZAin1998
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default SimpleLogin;
