import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Assessment as ReportIcon,
  FileDownload as ExportIcon,
  DateRange as DateIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line } from 'recharts';
import dayjs from 'dayjs';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { exportToPDF } from '../utils/pdfGenerator';

const Reports = () => {
  const { repairs, customers, spareParts, repairStatuses } = useData();
  
  const [reportType, setReportType] = useState('overview');
  const [dateFrom, setDateFrom] = useState(dayjs().subtract(30, 'day'));
  const [dateTo, setDateTo] = useState(dayjs());

  // Filter data by date range
  const filteredRepairs = repairs.filter(repair => {
    const repairDate = dayjs(repair.createdAt);
    return repairDate.isAfter(dateFrom, 'day') && repairDate.isBefore(dateTo, 'day');
  });

  // Calculate statistics
  const calculateStats = () => {
    const totalRepairs = filteredRepairs.length;
    const totalRevenue = filteredRepairs.reduce((sum, repair) => sum + repair.price, 0);
    const totalDebt = filteredRepairs.reduce((sum, repair) => sum + (repair.remainingDebt || 0), 0);
    const completedRepairs = filteredRepairs.filter(repair => repair.status === 'completed').length;
    const pendingRepairs = filteredRepairs.filter(repair => repair.status === 'pending').length;
    const deliveredRepairs = filteredRepairs.filter(repair => repair.status === 'delivered').length;

    return {
      totalRepairs,
      totalRevenue,
      totalDebt,
      completedRepairs,
      pendingRepairs,
      deliveredRepairs,
      completionRate: totalRepairs > 0 ? ((completedRepairs + deliveredRepairs) / totalRepairs * 100).toFixed(1) : 0
    };
  };

  // Status distribution data
  const getStatusData = () => {
    return Object.keys(repairStatuses).map(status => ({
      name: repairStatuses[status].label,
      value: filteredRepairs.filter(repair => repair.status === status).length,
      color: repairStatuses[status].color
    })).filter(item => item.value > 0);
  };

  // Monthly revenue data
  const getMonthlyData = () => {
    const monthlyData = {};
    filteredRepairs.forEach(repair => {
      const month = dayjs(repair.createdAt).format('YYYY-MM');
      if (!monthlyData[month]) {
        monthlyData[month] = { month, revenue: 0, count: 0 };
      }
      monthlyData[month].revenue += repair.price;
      monthlyData[month].count += 1;
    });
    
    return Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));
  };

  // Top customers data
  const getTopCustomers = () => {
    const customerData = {};
    filteredRepairs.forEach(repair => {
      if (!customerData[repair.customerName]) {
        customerData[repair.customerName] = { name: repair.customerName, count: 0, revenue: 0 };
      }
      customerData[repair.customerName].count += 1;
      customerData[repair.customerName].revenue += repair.price;
    });
    
    return Object.values(customerData)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
  };

  // Phone types data
  const getPhoneTypesData = () => {
    const phoneData = {};
    filteredRepairs.forEach(repair => {
      if (!phoneData[repair.phoneType]) {
        phoneData[repair.phoneType] = 0;
      }
      phoneData[repair.phoneType] += 1;
    });
    
    return Object.entries(phoneData)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);
  };

  const handleExport = () => {
    const stats = calculateStats();
    const reportData = [
      { field: 'إجمالي الطلبات', value: stats.totalRepairs },
      { field: 'إجمالي الإيرادات', value: `${stats.totalRevenue} ريال` },
      { field: 'إجمالي الديون', value: `${stats.totalDebt} ريال` },
      { field: 'الطلبات المكتملة', value: stats.completedRepairs },
      { field: 'الطلبات المعلقة', value: stats.pendingRepairs },
      { field: 'معدل الإنجاز', value: `${stats.completionRate}%` }
    ];

    const columns = [
      { field: 'field', header: 'البيان', width: 100 },
      { field: 'value', header: 'القيمة', width: 100 }
    ];

    exportToPDF(reportData, `تقرير النظام - ${dateFrom.format('YYYY-MM-DD')} إلى ${dateTo.format('YYYY-MM-DD')}`, columns);
  };

  const stats = calculateStats();
  const statusData = getStatusData();
  const monthlyData = getMonthlyData();
  const topCustomers = getTopCustomers();
  const phoneTypesData = getPhoneTypesData();

  const renderOverviewReport = () => (
    <Grid container spacing={3}>
      {/* Statistics Cards */}
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color="primary" fontWeight="bold">
              {stats.totalRepairs}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الطلبات
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color="success.main" fontWeight="bold">
              {stats.totalRevenue.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الإيرادات (ريال)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color="error.main" fontWeight="bold">
              {stats.totalDebt.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              إجمالي الديون (ريال)
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      <Grid item xs={12} md={3}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h4" color="info.main" fontWeight="bold">
              {stats.completionRate}%
            </Typography>
            <Typography variant="body2" color="text.secondary">
              معدل الإنجاز
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Status Distribution Chart */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              توزيع حالات الطلبات
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Monthly Revenue Chart */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              الإيرادات الشهرية
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="revenue" stroke="#2196F3" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Top Customers */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              أفضل العملاء
            </Typography>
            <TableContainer sx={{ maxHeight: 300 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>اسم العميل</TableCell>
                    <TableCell align="right">عدد الطلبات</TableCell>
                    <TableCell align="right">إجمالي الإنفاق</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {topCustomers.map((customer, index) => (
                    <TableRow key={index}>
                      <TableCell>{customer.name}</TableCell>
                      <TableCell align="right">{customer.count}</TableCell>
                      <TableCell align="right">{customer.revenue} ريال</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Phone Types */}
      <Grid item xs={12} md={6}>
        <Card sx={{ height: 400 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              أنواع الهواتف الأكثر صيانة
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={phoneTypesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#FF9800" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Layout>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            التقارير والإحصائيات
          </Typography>
          <Button
            variant="contained"
            startIcon={<ExportIcon />}
            onClick={handleExport}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            }}
          >
            تصدير التقرير
          </Button>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>نوع التقرير</InputLabel>
                  <Select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value)}
                    label="نوع التقرير"
                  >
                    <MenuItem value="overview">نظرة عامة</MenuItem>
                    <MenuItem value="financial">التقرير المالي</MenuItem>
                    <MenuItem value="customers">تقرير العملاء</MenuItem>
                    <MenuItem value="inventory">تقرير المخزون</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="من تاريخ"
                  value={dateFrom}
                  onChange={(newValue) => setDateFrom(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="إلى تاريخ"
                  value={dateTo}
                  onChange={(newValue) => setDateTo(newValue)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">
                  <DateIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  الفترة: {dateFrom.format('DD/MM/YYYY')} - {dateTo.format('DD/MM/YYYY')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Report Content */}
        {reportType === 'overview' && renderOverviewReport()}
        
        {reportType === 'financial' && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                التقرير المالي
              </Typography>
              <Typography variant="body1">
                سيتم إضافة التقرير المالي المفصل هنا...
              </Typography>
            </CardContent>
          </Card>
        )}

        {reportType === 'customers' && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تقرير العملاء
              </Typography>
              <Typography variant="body1">
                سيتم إضافة تقرير العملاء المفصل هنا...
              </Typography>
            </CardContent>
          </Card>
        )}

        {reportType === 'inventory' && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تقرير المخزون
              </Typography>
              <Typography variant="body1">
                سيتم إضافة تقرير المخزون المفصل هنا...
              </Typography>
            </CardContent>
          </Card>
        )}
      </Box>
    </Layout>
  );
};

export default Reports;
