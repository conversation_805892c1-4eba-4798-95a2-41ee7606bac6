import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  IconButton,
  Tooltip,
  Alert,
  Snackbar,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Payment as PaymentIcon,
  Visibility as ViewIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { generatePaymentReceipt } from '../utils/pdfGenerator';

const Customers = () => {
  const location = useLocation();
  const { customers, repairs, addCustomer, updateCustomer } = useData();
  
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [addDialog, setAddDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [paymentDialog, setPaymentDialog] = useState(false);
  const [viewDialog, setViewDialog] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  
  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    address: '',
    socialMedia: '',
    notes: ''
  });

  const [editData, setEditData] = useState({});
  const [paymentData, setPaymentData] = useState({
    amount: '',
    type: 'full',
    notes: ''
  });

  useEffect(() => {
    // Highlight specific customer if coming from search
    if (location.state?.highlightId) {
      const customer = customers.find(c => c.id === location.state.highlightId);
      if (customer) {
        setSelectedCustomer(customer);
        setViewDialog(true);
      }
    }
  }, [location.state, customers]);

  // Calculate customer debt and repair count
  const getCustomerStats = (customerId) => {
    const customerRepairs = repairs.filter(repair => repair.customerId === customerId);
    const totalRepairs = customerRepairs.length;
    const totalDebt = customerRepairs.reduce((sum, repair) => {
      if (repair.remainingDebt) {
        return sum + repair.remainingDebt;
      }
      return sum;
    }, 0);
    
    return { totalRepairs, totalDebt };
  };

  const handleAdd = () => {
    setNewCustomer({
      name: '',
      phone: '',
      address: '',
      socialMedia: '',
      notes: ''
    });
    setAddDialog(true);
  };

  const handleEdit = (customer) => {
    setSelectedCustomer(customer);
    setEditData({ ...customer });
    setEditDialog(true);
  };

  const handlePayment = (customer) => {
    setSelectedCustomer(customer);
    const stats = getCustomerStats(customer.id);
    setPaymentData({
      amount: stats.totalDebt.toString(),
      type: 'full',
      notes: ''
    });
    setPaymentDialog(true);
  };

  const handleView = (customer) => {
    setSelectedCustomer(customer);
    setViewDialog(true);
  };

  const confirmAdd = () => {
    if (!newCustomer.name || !newCustomer.phone) {
      setAlert({
        open: true,
        message: 'يرجى إدخال اسم العميل ورقم الهاتف على الأقل',
        severity: 'error'
      });
      return;
    }

    addCustomer(newCustomer);
    setAddDialog(false);
    setAlert({
      open: true,
      message: 'تم إضافة العميل بنجاح',
      severity: 'success'
    });
  };

  const confirmEdit = () => {
    updateCustomer(selectedCustomer.id, editData);
    setEditDialog(false);
    setAlert({
      open: true,
      message: 'تم تحديث بيانات العميل بنجاح',
      severity: 'success'
    });
  };

  const confirmPayment = () => {
    const stats = getCustomerStats(selectedCustomer.id);
    const paymentAmount = parseFloat(paymentData.amount);
    
    if (paymentAmount <= 0 || paymentAmount > stats.totalDebt) {
      setAlert({
        open: true,
        message: 'مبلغ الدفع غير صحيح',
        severity: 'error'
      });
      return;
    }

    // Create payment record
    const payment = {
      id: Date.now(),
      customerId: selectedCustomer.id,
      amount: paymentAmount,
      previousBalance: stats.totalDebt,
      remainingBalance: stats.totalDebt - paymentAmount,
      date: new Date().toISOString(),
      notes: paymentData.notes,
      type: paymentData.type
    };

    // Update customer repairs to reduce debt
    const customerRepairs = repairs.filter(repair => 
      repair.customerId === selectedCustomer.id && repair.remainingDebt > 0
    );

    let remainingPayment = paymentAmount;
    customerRepairs.forEach(repair => {
      if (remainingPayment > 0 && repair.remainingDebt > 0) {
        const paymentForThisRepair = Math.min(remainingPayment, repair.remainingDebt);
        repair.remainingDebt -= paymentForThisRepair;
        remainingPayment -= paymentForThisRepair;
        
        // Update repair status if fully paid
        if (repair.remainingDebt === 0) {
          repair.status = 'delivered';
        }
      }
    });

    // Generate payment receipt
    generatePaymentReceipt(payment, selectedCustomer);

    setPaymentDialog(false);
    setAlert({
      open: true,
      message: 'تم تسجيل الدفع بنجاح وطباعة الإيصال',
      severity: 'success'
    });
  };

  const columns = [
    {
      field: 'name',
      headerName: 'اسم العميل',
      width: 200
    },
    {
      field: 'phone',
      headerName: 'رقم الهاتف',
      width: 150
    },
    {
      field: 'address',
      headerName: 'العنوان',
      width: 200
    },
    {
      field: 'totalRepairs',
      headerName: 'عدد الطلبات',
      width: 120,
      renderCell: (params) => {
        const stats = getCustomerStats(params.row.id);
        return (
          <Chip
            label={stats.totalRepairs}
            color="primary"
            variant="outlined"
            size="small"
          />
        );
      }
    },
    {
      field: 'totalDebt',
      headerName: 'إجمالي الديون',
      width: 150,
      renderCell: (params) => {
        const stats = getCustomerStats(params.row.id);
        return (
          <Chip
            label={`${stats.totalDebt} ريال`}
            color={stats.totalDebt > 0 ? 'error' : 'success'}
            variant="filled"
            size="small"
          />
        );
      }
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ التسجيل',
      width: 150,
      renderCell: (params) => new Date(params.value).toLocaleDateString('ar')
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 250,
      sortable: false,
      renderCell: (params) => {
        const stats = getCustomerStats(params.row.id);
        return (
          <Box>
            <Tooltip title="عرض التفاصيل">
              <IconButton
                size="small"
                onClick={() => handleView(params.row)}
                color="primary"
              >
                <ViewIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="تعديل">
              <IconButton
                size="small"
                onClick={() => handleEdit(params.row)}
                color="info"
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
            {stats.totalDebt > 0 && (
              <Tooltip title="تسديد دين">
                <IconButton
                  size="small"
                  onClick={() => handlePayment(params.row)}
                  color="success"
                >
                  <PaymentIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        );
      }
    }
  ];

  return (
    <Layout>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            إدارة العملاء
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAdd}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            }}
          >
            إضافة عميل جديد
          </Button>
        </Box>

        <Card>
          <CardContent>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={customers}
                columns={columns}
                pageSize={10}
                rowsPerPageOptions={[10, 25, 50]}
                disableSelectionOnClick
                sx={{
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>

        {/* Add Customer Dialog */}
        <Dialog open={addDialog} onClose={() => setAddDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>إضافة عميل جديد</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="الاسم الثلاثي *"
                  value={newCustomer.name}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="رقم الهاتف *"
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="العنوان"
                  value={newCustomer.address}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, address: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="برامج التواصل"
                  value={newCustomer.socialMedia}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, socialMedia: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={3}
                  value={newCustomer.notes}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddDialog(false)}>إلغاء</Button>
            <Button onClick={confirmAdd} variant="contained">إضافة العميل</Button>
          </DialogActions>
        </Dialog>

        {/* Edit Customer Dialog */}
        <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>تعديل بيانات العميل</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="الاسم الثلاثي"
                  value={editData.name || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="رقم الهاتف"
                  value={editData.phone || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, phone: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="العنوان"
                  value={editData.address || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, address: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="برامج التواصل"
                  value={editData.socialMedia || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, socialMedia: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={3}
                  value={editData.notes || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialog(false)}>إلغاء</Button>
            <Button onClick={confirmEdit} variant="contained">حفظ التغييرات</Button>
          </DialogActions>
        </Dialog>

        {/* Payment Dialog */}
        <Dialog open={paymentDialog} onClose={() => setPaymentDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>تسديد دين العميل</DialogTitle>
          <DialogContent>
            {selectedCustomer && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom>
                  العميل: {selectedCustomer.name}
                </Typography>
                <Typography variant="body1" color="error" gutterBottom>
                  إجمالي الدين: {getCustomerStats(selectedCustomer.id).totalDebt} ريال
                </Typography>
                
                <Grid container spacing={2} sx={{ mt: 2 }}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="المبلغ المدفوع"
                      type="number"
                      value={paymentData.amount}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                      InputProps={{
                        endAdornment: 'ريال'
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="ملاحظات"
                      multiline
                      rows={3}
                      value={paymentData.notes}
                      onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setPaymentDialog(false)}>إلغاء</Button>
            <Button onClick={confirmPayment} variant="contained" color="success">
              تسديد وطباعة الإيصال
            </Button>
          </DialogActions>
        </Dialog>

        {/* View Customer Dialog */}
        <Dialog open={viewDialog} onClose={() => setViewDialog(false)} maxWidth="lg" fullWidth>
          <DialogTitle>تفاصيل العميل</DialogTitle>
          <DialogContent>
            {selectedCustomer && (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      معلومات العميل
                    </Typography>
                    <Typography><strong>الاسم:</strong> {selectedCustomer.name}</Typography>
                    <Typography><strong>الهاتف:</strong> {selectedCustomer.phone}</Typography>
                    <Typography><strong>العنوان:</strong> {selectedCustomer.address || 'غير محدد'}</Typography>
                    <Typography><strong>برامج التواصل:</strong> {selectedCustomer.socialMedia || 'غير محدد'}</Typography>
                    <Typography><strong>تاريخ التسجيل:</strong> {new Date(selectedCustomer.createdAt).toLocaleDateString('ar')}</Typography>
                    {selectedCustomer.notes && (
                      <Typography><strong>ملاحظات:</strong> {selectedCustomer.notes}</Typography>
                    )}
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      إحصائيات العميل
                    </Typography>
                    <Typography>
                      <strong>عدد الطلبات:</strong> {getCustomerStats(selectedCustomer.id).totalRepairs}
                    </Typography>
                    <Typography color={getCustomerStats(selectedCustomer.id).totalDebt > 0 ? 'error' : 'success'}>
                      <strong>إجمالي الديون:</strong> {getCustomerStats(selectedCustomer.id).totalDebt} ريال
                    </Typography>
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                      طلبات الصيانة
                    </Typography>
                    <List>
                      {repairs
                        .filter(repair => repair.customerId === selectedCustomer.id)
                        .map((repair, index) => (
                          <React.Fragment key={repair.id}>
                            <ListItem>
                              <ListItemText
                                primary={`${repair.phoneType} - ${repair.problem}`}
                                secondary={
                                  <Box>
                                    <Typography variant="body2">
                                      السعر: {repair.price} ريال | التاريخ: {new Date(repair.createdAt).toLocaleDateString('ar')}
                                    </Typography>
                                    {repair.remainingDebt > 0 && (
                                      <Chip
                                        label={`دين: ${repair.remainingDebt} ريال`}
                                        color="error"
                                        size="small"
                                        sx={{ mt: 1 }}
                                      />
                                    )}
                                  </Box>
                                }
                              />
                            </ListItem>
                            {index < repairs.filter(r => r.customerId === selectedCustomer.id).length - 1 && <Divider />}
                          </React.Fragment>
                        ))}
                    </List>
                  </Paper>
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewDialog(false)}>إغلاق</Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setAlert(prev => ({ ...prev, open: false }))}
            severity={alert.severity}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default Customers;
