import jsPDF from 'jspdf';
import JsBarcode from 'jsbarcode';

// Function to generate repair receipt (Arabic/Iraqi)
export const generateRepairReceipt = (repair, settings = {}) => {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a5'
  });

  // Default Iraqi settings
  const defaultSettings = {
    centerName: 'مركز الرافدين لصيانة الهواتف المحمولة',
    address: 'بغداد - الكرادة - شارع الرئيسي',
    phone: '***********',
    email: '<EMAIL>',
    currency: 'د.ع',
    country: 'العراق',
    city: 'بغداد',
    socialMedia: {
      facebook: 'RafidainMobile',
      instagram: 'rafidain_mobile',
      whatsapp: '***********',
      telegram: '@rafidain_mobile'
    },
    businessInfo: {
      licenseNumber: 'TR-2024-001',
      taxNumber: 'TAX-*********'
    }
  };

  const config = { ...defaultSettings, ...settings };

  // Set font for Arabic support
  doc.setFont('helvetica');

  // Header with Iraqi flag colors (Red, White, Black, Green)
  doc.setFontSize(18);
  doc.setTextColor(27, 94, 32); // Green color for header
  doc.text(config.centerName, 105, 20, { align: 'center' });

  // Business info
  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`العنوان: ${config.address}`, 105, 28, { align: 'center' });
  doc.text(`الهاتف: ${config.phone}`, 105, 33, { align: 'center' });
  if (config.email) {
    doc.text(`البريد الإلكتروني: ${config.email}`, 105, 38, { align: 'center' });
  }

  // License and tax info
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(`رقم الرخصة: ${config.businessInfo.licenseNumber}`, 20, 45, { align: 'left' });
  doc.text(`الرقم الضريبي: ${config.businessInfo.taxNumber}`, 120, 45, { align: 'right' });

  // Social media (Arabic)
  if (config.socialMedia.facebook || config.socialMedia.instagram || config.socialMedia.whatsapp) {
    let socialText = 'تابعونا: ';
    if (config.socialMedia.facebook) socialText += `فيسبوك: ${config.socialMedia.facebook} `;
    if (config.socialMedia.instagram) socialText += `انستغرام: ${config.socialMedia.instagram} `;
    if (config.socialMedia.whatsapp) socialText += `واتساب: ${config.socialMedia.whatsapp} `;
    if (config.socialMedia.telegram) socialText += `تلغرام: ${config.socialMedia.telegram}`;
    doc.setFontSize(8);
    doc.text(socialText, 105, 50, { align: 'center' });
  }

  // Decorative line with Iraqi colors
  doc.setLineWidth(1);
  doc.setDrawColor(211, 47, 47); // Red
  doc.line(10, 55, 138, 55);
  doc.setDrawColor(0, 0, 0); // Black
  doc.line(10, 56, 138, 56);
  doc.setDrawColor(27, 94, 32); // Green
  doc.line(10, 57, 138, 57);

  // Receipt title (Arabic)
  doc.setFontSize(16);
  doc.setTextColor(211, 47, 47); // Red
  doc.text('إيصال صيانة هاتف محمول', 105, 68, { align: 'center' });

  // Serial number (in red with Arabic)
  doc.setFontSize(12);
  doc.setTextColor(255, 0, 0);
  doc.text(`رقم الإيصال: ${repair.serialNumber}`, 105, 78, { align: 'center' });

  // Customer details (Arabic)
  doc.setFontSize(11);
  doc.setTextColor(0, 0, 0);

  let yPos = 90;

  // Customer info section
  doc.setFontSize(12);
  doc.setTextColor(27, 94, 32);
  doc.text('بيانات العميل:', 15, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`اسم العميل: ${repair.customerName}`, 15, yPos);
  yPos += 7;
  doc.text(`رقم الهاتف: ${repair.customerPhone}`, 15, yPos);
  yPos += 10;

  // Device info section
  doc.setFontSize(12);
  doc.setTextColor(27, 94, 32);
  doc.text('بيانات الجهاز:', 15, yPos);
  yPos += 10;

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`نوع الجهاز: ${repair.phoneType}`, 15, yPos);
  yPos += 7;
  doc.text(`المشكلة: ${repair.problem}`, 15, yPos);
  yPos += 10;

  // Financial info section
  doc.setFontSize(12);
  doc.setTextColor(27, 94, 32);
  doc.text('البيانات المالية:', 15, yPos);
  yPos += 10;

  doc.setFontSize(11);
  doc.setTextColor(0, 0, 0);
  doc.text(`تكلفة الصيانة: ${repair.price.toLocaleString()} ${config.currency}`, 15, yPos);
  yPos += 7;

  // Payment type (Arabic)
  let paymentText = 'طريقة الدفع: ';
  switch (repair.paymentType) {
    case 'cash':
      paymentText += 'نقداً';
      break;
    case 'partial':
      paymentText += `دفع جزئي (${repair.partialAmount?.toLocaleString()} ${config.currency})`;
      break;
    case 'deferred':
      paymentText += 'آجل بعد الصيانة';
      break;
    default:
      paymentText += repair.paymentType;
  }
  doc.text(paymentText, 15, yPos);
  yPos += 7;

  // Date (Arabic)
  const arabicDate = new Date(repair.date).toLocaleDateString('ar-IQ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
  doc.text(`التاريخ: ${arabicDate}`, 15, yPos);
  yPos += 7;

  if (repair.notes) {
    doc.text(`ملاحظات: ${repair.notes}`, 15, yPos);
    yPos += 7;
  }

  // Decorative line separator
  yPos += 5;
  doc.setLineWidth(0.5);
  doc.setDrawColor(200, 200, 200);
  doc.line(10, yPos, 138, yPos);

  // Generate barcode
  yPos += 10;
  const canvas = document.createElement('canvas');
  JsBarcode(canvas, repair.serialNumber, {
    format: 'CODE128',
    width: 2,
    height: 35,
    displayValue: true,
    fontSize: 12,
    textMargin: 5
  });

  // Add barcode to PDF
  const barcodeDataURL = canvas.toDataURL('image/png');
  doc.addImage(barcodeDataURL, 'PNG', 35, yPos, 78, 20);

  // Thank you message (Arabic)
  yPos += 30;
  doc.setFontSize(12);
  doc.setTextColor(27, 94, 32);
  doc.text('شكراً لثقتكم بنا', 105, yPos, { align: 'center' });
  yPos += 6;
  doc.setFontSize(10);
  doc.setTextColor(100, 100, 100);
  doc.text('نتطلع لخدمتكم دائماً - فريق مركز الرافدين', 105, yPos, { align: 'center' });

  // Warranty info
  yPos += 8;
  doc.setFontSize(9);
  doc.setTextColor(211, 47, 47);
  doc.text('ضمان الصيانة: 30 يوم من تاريخ الاستلام', 105, yPos, { align: 'center' });

  // Contact info
  yPos += 6;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text('للاستفسارات: اتصل بنا على الرقم أعلاه أو زوروا صفحتنا على مواقع التواصل', 105, yPos, { align: 'center' });

  // Footer with generation info
  doc.setFontSize(7);
  doc.setTextColor(150, 150, 150);
  const generationDate = new Date().toLocaleDateString('ar-IQ', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
  doc.text(`تم إنشاء الإيصال في: ${generationDate}`, 105, 200, { align: 'center' });

  // Save the PDF with Arabic filename
  doc.save(`ايصال-صيانة-${repair.serialNumber}.pdf`);
};

// Function to generate payment receipt
export const generatePaymentReceipt = (payment, customer, settings = {}) => {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a5'
  });

  // Default settings
  const defaultSettings = {
    centerName: 'مركز صيانة الموبايلات',
    address: 'العنوان',
    phone: '*********'
  };

  const config = { ...defaultSettings, ...settings };

  // Set font
  doc.setFont('helvetica');

  // Header
  doc.setFontSize(16);
  doc.setTextColor(220, 20, 60);
  doc.text(config.centerName, 105, 20, { align: 'center' });

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`Address: ${config.address}`, 105, 30, { align: 'center' });
  doc.text(`Phone: ${config.phone}`, 105, 35, { align: 'center' });

  // Line separator
  doc.setLineWidth(0.5);
  doc.line(10, 45, 138, 45);

  // Receipt title
  doc.setFontSize(14);
  doc.setTextColor(220, 20, 60);
  doc.text('PAYMENT RECEIPT', 105, 55, { align: 'center' });

  // Receipt number
  doc.setFontSize(12);
  doc.setTextColor(255, 0, 0);
  doc.text(`Receipt No: ${payment.id}`, 105, 65, { align: 'center' });

  // Customer and payment details
  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  
  let yPos = 80;
  doc.text(`Customer: ${customer.name}`, 15, yPos);
  yPos += 8;
  doc.text(`Phone: ${customer.phone}`, 15, yPos);
  yPos += 8;
  doc.text(`Payment Date: ${new Date(payment.date).toLocaleDateString()}`, 15, yPos);
  yPos += 8;
  doc.text(`Amount Paid: ${payment.amount} SAR`, 15, yPos);
  yPos += 8;
  doc.text(`Previous Balance: ${payment.previousBalance} SAR`, 15, yPos);
  yPos += 8;
  doc.text(`Remaining Balance: ${payment.remainingBalance} SAR`, 15, yPos);
  yPos += 8;

  if (payment.notes) {
    doc.text(`Notes: ${payment.notes}`, 15, yPos);
    yPos += 8;
  }

  // Line separator
  doc.line(10, yPos + 5, 138, yPos + 5);

  // Generate barcode for payment
  const canvas = document.createElement('canvas');
  JsBarcode(canvas, `PAY${payment.id}`, {
    format: 'CODE128',
    width: 2,
    height: 40,
    displayValue: false
  });

  // Add barcode to PDF
  const barcodeDataURL = canvas.toDataURL('image/png');
  doc.addImage(barcodeDataURL, 'PNG', 40, yPos + 10, 68, 15);

  // Thank you message
  yPos += 35;
  doc.setFontSize(10);
  doc.setTextColor(0, 100, 0);
  doc.text('Thank you for your payment!', 105, yPos, { align: 'center' });

  // Footer
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 200, { align: 'center' });

  // Save the PDF
  doc.save(`payment-receipt-${payment.id}.pdf`);
};

// Function to export data to PDF
export const exportToPDF = (data, title, columns) => {
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  doc.setFont('helvetica');
  doc.setFontSize(16);
  doc.text(title, 148, 20, { align: 'center' });

  // Add table headers
  doc.setFontSize(10);
  let yPos = 40;
  let xPos = 20;
  
  columns.forEach((column, index) => {
    doc.text(column.header, xPos, yPos);
    xPos += column.width || 40;
  });

  // Add table data
  yPos += 10;
  data.forEach((row, rowIndex) => {
    if (yPos > 180) {
      doc.addPage();
      yPos = 20;
    }
    
    xPos = 20;
    columns.forEach((column, colIndex) => {
      const value = row[column.field] || '';
      doc.text(String(value), xPos, yPos);
      xPos += column.width || 40;
    });
    yPos += 8;
  });

  doc.save(`${title.replace(/\s+/g, '-').toLowerCase()}.pdf`);
};
