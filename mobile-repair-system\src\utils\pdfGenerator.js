import jsPDF from 'jspdf';
import JsBarcode from 'jsbarcode';

// Function to generate repair receipt
export const generateRepairReceipt = (repair, settings = {}) => {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a5'
  });

  // Default settings
  const defaultSettings = {
    centerName: 'مركز صيانة الموبايلات',
    address: 'العنوان',
    phone: '123456789',
    socialMedia: {
      facebook: '',
      instagram: '',
      whatsapp: ''
    }
  };

  const config = { ...defaultSettings, ...settings };

  // Set font (Note: Arabic text might not display correctly without proper font)
  doc.setFont('helvetica');

  // Header
  doc.setFontSize(16);
  doc.setTextColor(220, 20, 60); // Red color for header
  doc.text(config.centerName, 105, 20, { align: 'center' });

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`Address: ${config.address}`, 105, 30, { align: 'center' });
  doc.text(`Phone: ${config.phone}`, 105, 35, { align: 'center' });

  // Social media
  if (config.socialMedia.facebook || config.socialMedia.instagram || config.socialMedia.whatsapp) {
    let socialText = 'Social Media: ';
    if (config.socialMedia.facebook) socialText += `FB: ${config.socialMedia.facebook} `;
    if (config.socialMedia.instagram) socialText += `IG: ${config.socialMedia.instagram} `;
    if (config.socialMedia.whatsapp) socialText += `WA: ${config.socialMedia.whatsapp}`;
    doc.text(socialText, 105, 40, { align: 'center' });
  }

  // Line separator
  doc.setLineWidth(0.5);
  doc.line(10, 45, 138, 45);

  // Receipt title
  doc.setFontSize(14);
  doc.setTextColor(220, 20, 60);
  doc.text('REPAIR RECEIPT', 105, 55, { align: 'center' });

  // Serial number (in red)
  doc.setFontSize(12);
  doc.setTextColor(255, 0, 0);
  doc.text(`Serial No: ${repair.serialNumber}`, 105, 65, { align: 'center' });

  // Customer details
  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  
  let yPos = 80;
  doc.text(`Customer Name: ${repair.customerName}`, 15, yPos);
  yPos += 8;
  doc.text(`Phone: ${repair.customerPhone}`, 15, yPos);
  yPos += 8;
  doc.text(`Device Type: ${repair.phoneType}`, 15, yPos);
  yPos += 8;
  doc.text(`Problem: ${repair.problem}`, 15, yPos);
  yPos += 8;
  doc.text(`Price: ${repair.price} SAR`, 15, yPos);
  yPos += 8;
  
  // Payment type
  let paymentText = 'Payment: ';
  switch (repair.paymentType) {
    case 'cash':
      paymentText += 'Cash';
      break;
    case 'partial':
      paymentText += `Partial (${repair.partialAmount} SAR)`;
      break;
    case 'deferred':
      paymentText += 'Deferred after repair';
      break;
    default:
      paymentText += repair.paymentType;
  }
  doc.text(paymentText, 15, yPos);
  yPos += 8;

  doc.text(`Date: ${new Date(repair.date).toLocaleDateString()}`, 15, yPos);
  yPos += 8;

  if (repair.notes) {
    doc.text(`Notes: ${repair.notes}`, 15, yPos);
    yPos += 8;
  }

  // Line separator
  doc.line(10, yPos + 5, 138, yPos + 5);

  // Generate barcode
  const canvas = document.createElement('canvas');
  JsBarcode(canvas, repair.serialNumber, {
    format: 'CODE128',
    width: 2,
    height: 40,
    displayValue: false
  });

  // Add barcode to PDF
  const barcodeDataURL = canvas.toDataURL('image/png');
  doc.addImage(barcodeDataURL, 'PNG', 40, yPos + 10, 68, 15);

  // Thank you message
  yPos += 35;
  doc.setFontSize(10);
  doc.setTextColor(0, 100, 0);
  doc.text('Thank you for choosing our service!', 105, yPos, { align: 'center' });
  doc.text('We appreciate your trust in us.', 105, yPos + 5, { align: 'center' });

  // Footer
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 200, { align: 'center' });

  // Save the PDF
  doc.save(`repair-receipt-${repair.serialNumber}.pdf`);
};

// Function to generate payment receipt
export const generatePaymentReceipt = (payment, customer, settings = {}) => {
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a5'
  });

  // Default settings
  const defaultSettings = {
    centerName: 'مركز صيانة الموبايلات',
    address: 'العنوان',
    phone: '123456789'
  };

  const config = { ...defaultSettings, ...settings };

  // Set font
  doc.setFont('helvetica');

  // Header
  doc.setFontSize(16);
  doc.setTextColor(220, 20, 60);
  doc.text(config.centerName, 105, 20, { align: 'center' });

  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  doc.text(`Address: ${config.address}`, 105, 30, { align: 'center' });
  doc.text(`Phone: ${config.phone}`, 105, 35, { align: 'center' });

  // Line separator
  doc.setLineWidth(0.5);
  doc.line(10, 45, 138, 45);

  // Receipt title
  doc.setFontSize(14);
  doc.setTextColor(220, 20, 60);
  doc.text('PAYMENT RECEIPT', 105, 55, { align: 'center' });

  // Receipt number
  doc.setFontSize(12);
  doc.setTextColor(255, 0, 0);
  doc.text(`Receipt No: ${payment.id}`, 105, 65, { align: 'center' });

  // Customer and payment details
  doc.setFontSize(10);
  doc.setTextColor(0, 0, 0);
  
  let yPos = 80;
  doc.text(`Customer: ${customer.name}`, 15, yPos);
  yPos += 8;
  doc.text(`Phone: ${customer.phone}`, 15, yPos);
  yPos += 8;
  doc.text(`Payment Date: ${new Date(payment.date).toLocaleDateString()}`, 15, yPos);
  yPos += 8;
  doc.text(`Amount Paid: ${payment.amount} SAR`, 15, yPos);
  yPos += 8;
  doc.text(`Previous Balance: ${payment.previousBalance} SAR`, 15, yPos);
  yPos += 8;
  doc.text(`Remaining Balance: ${payment.remainingBalance} SAR`, 15, yPos);
  yPos += 8;

  if (payment.notes) {
    doc.text(`Notes: ${payment.notes}`, 15, yPos);
    yPos += 8;
  }

  // Line separator
  doc.line(10, yPos + 5, 138, yPos + 5);

  // Generate barcode for payment
  const canvas = document.createElement('canvas');
  JsBarcode(canvas, `PAY${payment.id}`, {
    format: 'CODE128',
    width: 2,
    height: 40,
    displayValue: false
  });

  // Add barcode to PDF
  const barcodeDataURL = canvas.toDataURL('image/png');
  doc.addImage(barcodeDataURL, 'PNG', 40, yPos + 10, 68, 15);

  // Thank you message
  yPos += 35;
  doc.setFontSize(10);
  doc.setTextColor(0, 100, 0);
  doc.text('Thank you for your payment!', 105, yPos, { align: 'center' });

  // Footer
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 200, { align: 'center' });

  // Save the PDF
  doc.save(`payment-receipt-${payment.id}.pdf`);
};

// Function to export data to PDF
export const exportToPDF = (data, title, columns) => {
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  doc.setFont('helvetica');
  doc.setFontSize(16);
  doc.text(title, 148, 20, { align: 'center' });

  // Add table headers
  doc.setFontSize(10);
  let yPos = 40;
  let xPos = 20;
  
  columns.forEach((column, index) => {
    doc.text(column.header, xPos, yPos);
    xPos += column.width || 40;
  });

  // Add table data
  yPos += 10;
  data.forEach((row, rowIndex) => {
    if (yPos > 180) {
      doc.addPage();
      yPos = 20;
    }
    
    xPos = 20;
    columns.forEach((column, colIndex) => {
      const value = row[column.field] || '';
      doc.text(String(value), xPos, yPos);
      xPos += column.width || 40;
    });
    yPos += 8;
  });

  doc.save(`${title.replace(/\s+/g, '-').toLowerCase()}.pdf`);
};
