import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Typography, Button, Card, CardContent } from '@mui/material';

// Simple Test Component
const SimpleTest = () => {
  return (
    <Box sx={{ p: 4, minHeight: '100vh', backgroundColor: '#f5f5f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <Card sx={{ maxWidth: 600, mx: 'auto' }}>
        <CardContent sx={{ textAlign: 'center', p: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom color="primary">
            🇮🇶 نظام الرافدين للصيانة
          </Typography>

          <Typography variant="h6" gutterBottom sx={{ mb: 3 }}>
            النظام يعمل بشكل صحيح!
          </Typography>

          <Box sx={{ mb: 3, p: 2, backgroundColor: '#e8f5e8', borderRadius: 2 }}>
            <Typography variant="body1" color="success.main">
              ✅ React يعمل بشكل مثالي
            </Typography>
            <Typography variant="body1" color="success.main">
              ✅ Material-UI يعمل
            </Typography>
            <Typography variant="body1" color="success.main">
              ✅ الخطوط العربية تعمل
            </Typography>
          </Box>

          <Button
            variant="contained"
            size="large"
            onClick={() => alert('النظام يعمل!')}
            sx={{
              background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
              fontSize: '1.2rem',
              px: 4,
              py: 2
            }}
          >
            اختبار النظام
          </Button>

          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary">
              إذا رأيت هذه الرسالة، فإن النظام الأساسي يعمل بشكل صحيح
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

// Simple theme
const theme = createTheme({
  direction: 'rtl',
  palette: {
    primary: {
      main: '#1B5E20',
    },
    secondary: {
      main: '#D32F2F',
    },
  },
  typography: {
    fontFamily: '"Segoe UI", "Tahoma", "Arial", sans-serif',
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          <Route path="/" element={<SimpleTest />} />
          <Route path="/test" element={<SimpleTest />} />
          <Route path="*" element={<SimpleTest />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
