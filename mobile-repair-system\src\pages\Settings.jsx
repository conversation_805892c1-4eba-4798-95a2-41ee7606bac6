import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Snackbar,
  Avatar,
  IconButton,
  Paper
} from '@mui/material';
import {
  Save as SaveIcon,
  Backup as BackupIcon,
  Restore as RestoreIcon,
  PhotoCamera as PhotoIcon,
  Palette as PaletteIcon,
  Print as PrintIcon,
  Security as SecurityIcon,
  Business as BusinessIcon
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';

const Settings = () => {
  const { settings, setSettings, saveData } = useData();
  
  const [localSettings, setLocalSettings] = useState({ ...settings });
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  const [activeTab, setActiveTab] = useState('general');

  const handleSave = () => {
    setSettings(localSettings);
    saveData('settings', localSettings);
    setAlert({
      open: true,
      message: 'تم حفظ الإعدادات بنجاح',
      severity: 'success'
    });
  };

  const handleBackup = () => {
    const data = {
      repairs: JSON.parse(localStorage.getItem('repairs') || '[]'),
      customers: JSON.parse(localStorage.getItem('customers') || '[]'),
      spareParts: JSON.parse(localStorage.getItem('spareParts') || '[]'),
      documents: JSON.parse(localStorage.getItem('documents') || '[]'),
      activities: JSON.parse(localStorage.getItem('activities') || '[]'),
      settings: JSON.parse(localStorage.getItem('settings') || '{}')
    };

    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    setAlert({
      open: true,
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      severity: 'success'
    });
  };

  const handleRestore = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          
          // Restore data to localStorage
          Object.keys(data).forEach(key => {
            localStorage.setItem(key, JSON.stringify(data[key]));
          });

          setAlert({
            open: true,
            message: 'تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل الصفحة.',
            severity: 'success'
          });

          // Reload page after 2 seconds
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } catch (error) {
          setAlert({
            open: true,
            message: 'خطأ في استعادة النسخة الاحتياطية',
            severity: 'error'
          });
        }
      };
      reader.readAsText(file);
    }
  };

  const settingsTabs = [
    { id: 'general', label: 'عام', icon: <BusinessIcon /> },
    { id: 'appearance', label: 'المظهر', icon: <PaletteIcon /> },
    { id: 'printing', label: 'الطباعة', icon: <PrintIcon /> },
    { id: 'security', label: 'الأمان', icon: <SecurityIcon /> },
    { id: 'backup', label: 'النسخ الاحتياطي', icon: <BackupIcon /> }
  ];

  const renderGeneralSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="اسم النظام"
          value={localSettings.systemName || ''}
          onChange={(e) => setLocalSettings(prev => ({ ...prev, systemName: e.target.value }))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="اسم المركز"
          value={localSettings.centerName || ''}
          onChange={(e) => setLocalSettings(prev => ({ ...prev, centerName: e.target.value }))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="العنوان"
          value={localSettings.address || ''}
          onChange={(e) => setLocalSettings(prev => ({ ...prev, address: e.target.value }))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="رقم الهاتف"
          value={localSettings.phone || ''}
          onChange={(e) => setLocalSettings(prev => ({ ...prev, phone: e.target.value }))}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="فيسبوك"
          value={localSettings.socialMedia?.facebook || ''}
          onChange={(e) => setLocalSettings(prev => ({
            ...prev,
            socialMedia: { ...prev.socialMedia, facebook: e.target.value }
          }))}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="إنستغرام"
          value={localSettings.socialMedia?.instagram || ''}
          onChange={(e) => setLocalSettings(prev => ({
            ...prev,
            socialMedia: { ...prev.socialMedia, instagram: e.target.value }
          }))}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="واتساب"
          value={localSettings.socialMedia?.whatsapp || ''}
          onChange={(e) => setLocalSettings(prev => ({
            ...prev,
            socialMedia: { ...prev.socialMedia, whatsapp: e.target.value }
          }))}
        />
      </Grid>
      <Grid item xs={12}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{ width: 80, height: 80 }}
            src={localSettings.logo}
          >
            <PhotoIcon />
          </Avatar>
          <Box>
            <Typography variant="h6">شعار المركز</Typography>
            <Button
              variant="outlined"
              component="label"
              startIcon={<PhotoIcon />}
            >
              رفع شعار
              <input
                type="file"
                hidden
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      setLocalSettings(prev => ({ ...prev, logo: event.target.result }));
                    };
                    reader.readAsDataURL(file);
                  }
                }}
              />
            </Button>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );

  const renderAppearanceSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="اللون الأساسي"
          type="color"
          value={localSettings.colors?.primary || '#1976d2'}
          onChange={(e) => setLocalSettings(prev => ({
            ...prev,
            colors: { ...prev.colors, primary: e.target.value }
          }))}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="اللون الثانوي"
          type="color"
          value={localSettings.colors?.secondary || '#dc004e'}
          onChange={(e) => setLocalSettings(prev => ({
            ...prev,
            colors: { ...prev.colors, secondary: e.target.value }
          }))}
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={localSettings.darkMode || false}
              onChange={(e) => setLocalSettings(prev => ({ ...prev, darkMode: e.target.checked }))}
            />
          }
          label="الوضع المظلم"
        />
      </Grid>
    </Grid>
  );

  const renderPrintingSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>قياس المستندات</InputLabel>
          <Select
            value={localSettings.documentSettings?.size || 'A5'}
            onChange={(e) => setLocalSettings(prev => ({
              ...prev,
              documentSettings: { ...prev.documentSettings, size: e.target.value }
            }))}
            label="قياس المستندات"
          >
            <MenuItem value="A4">A4</MenuItem>
            <MenuItem value="A5">A5</MenuItem>
            <MenuItem value="thermal">طابعة حرارية</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>نوع الطابعة</InputLabel>
          <Select
            value={localSettings.documentSettings?.printerType || 'normal'}
            onChange={(e) => setLocalSettings(prev => ({
              ...prev,
              documentSettings: { ...prev.documentSettings, printerType: e.target.value }
            }))}
            label="نوع الطابعة"
          >
            <MenuItem value="normal">طابعة عادية</MenuItem>
            <MenuItem value="thermal">طابعة حرارية</MenuItem>
            <MenuItem value="laser">طابعة ليزر</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={localSettings.documentSettings?.autoprint || false}
              onChange={(e) => setLocalSettings(prev => ({
                ...prev,
                documentSettings: { ...prev.documentSettings, autoprint: e.target.checked }
              }))}
            />
          }
          label="طباعة تلقائية للإيصالات"
        />
      </Grid>
    </Grid>
  );

  const renderSecuritySettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="اسم المستخدم الحالي"
          value="abd"
          disabled
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <TextField
          fullWidth
          label="كلمة المرور الجديدة"
          type="password"
          placeholder="اتركها فارغة للاحتفاظ بالحالية"
        />
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={localSettings.security?.autoLogout || false}
              onChange={(e) => setLocalSettings(prev => ({
                ...prev,
                security: { ...prev.security, autoLogout: e.target.checked }
              }))}
            />
          }
          label="تسجيل خروج تلقائي بعد فترة عدم نشاط"
        />
      </Grid>
    </Grid>
  );

  const renderBackupSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            النسخ الاحتياطي
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            قم بإنشاء نسخة احتياطية من جميع بيانات النظام أو استعادة نسخة احتياطية سابقة.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<BackupIcon />}
              onClick={handleBackup}
            >
              إنشاء نسخة احتياطية
            </Button>
            <Button
              variant="outlined"
              component="label"
              startIcon={<RestoreIcon />}
            >
              استعادة نسخة احتياطية
              <input
                type="file"
                hidden
                accept=".json"
                onChange={handleRestore}
              />
            </Button>
          </Box>
        </Paper>
      </Grid>
      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={localSettings.backup?.autoBackup || false}
              onChange={(e) => setLocalSettings(prev => ({
                ...prev,
                backup: { ...prev.backup, autoBackup: e.target.checked }
              }))}
            />
          }
          label="نسخ احتياطي تلقائي يومي"
        />
      </Grid>
    </Grid>
  );

  return (
    <Layout>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            إعدادات النظام
          </Typography>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            sx={{
              background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
            }}
          >
            حفظ الإعدادات
          </Button>
        </Box>

        <Grid container spacing={3}>
          {/* Settings Navigation */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  أقسام الإعدادات
                </Typography>
                {settingsTabs.map((tab) => (
                  <Button
                    key={tab.id}
                    fullWidth
                    variant={activeTab === tab.id ? 'contained' : 'text'}
                    startIcon={tab.icon}
                    onClick={() => setActiveTab(tab.id)}
                    sx={{
                      justifyContent: 'flex-start',
                      mb: 1,
                      ...(activeTab === tab.id && {
                        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                      })
                    }}
                  >
                    {tab.label}
                  </Button>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Settings Content */}
          <Grid item xs={12} md={9}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {settingsTabs.find(tab => tab.id === activeTab)?.label}
                </Typography>
                <Divider sx={{ mb: 3 }} />

                {activeTab === 'general' && renderGeneralSettings()}
                {activeTab === 'appearance' && renderAppearanceSettings()}
                {activeTab === 'printing' && renderPrintingSettings()}
                {activeTab === 'security' && renderSecuritySettings()}
                {activeTab === 'backup' && renderBackupSettings()}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setAlert(prev => ({ ...prev, open: false }))}
            severity={alert.severity}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default Settings;
