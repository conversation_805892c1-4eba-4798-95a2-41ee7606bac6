import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Build as BuildIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';

const Dashboard = () => {
  const { repairs, customers, spareParts, repairStatuses } = useData();

  // Calculate statistics
  const totalRepairs = repairs.length;
  const totalCustomers = customers.length;
  const totalSpareParts = spareParts.length;
  const totalDebt = customers.reduce((sum, customer) => sum + (customer.debt || 0), 0);

  // Repair status statistics
  const statusStats = Object.keys(repairStatuses).map(status => ({
    name: repairStatuses[status].label,
    value: repairs.filter(repair => repair.status === status).length,
    color: repairStatuses[status].color
  }));

  // Recent repairs
  const recentRepairs = repairs
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5);

  // Monthly repair data (last 6 months)
  const monthlyData = [];
  for (let i = 5; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthName = date.toLocaleDateString('ar', { month: 'long' });
    const monthRepairs = repairs.filter(repair => {
      const repairDate = new Date(repair.createdAt);
      return repairDate.getMonth() === date.getMonth() && 
             repairDate.getFullYear() === date.getFullYear();
    }).length;
    monthlyData.push({ month: monthName, repairs: monthRepairs });
  }

  // Low stock spare parts
  const lowStockParts = spareParts.filter(part => part.quantity < 5);

  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <Card
      sx={{
        background: `linear-gradient(45deg, ${color} 30%, ${color}CC 90%)`,
        color: 'white',
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          right: 0,
          width: '100px',
          height: '100px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          transform: 'translate(30px, -30px)',
        }
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value.toLocaleString()}
            </Typography>
            <Typography variant="h6" sx={{ opacity: 0.9 }}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="body2" sx={{ opacity: 0.8, mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              width: 60,
              height: 60,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Layout>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" color="primary">
          لوحة التحكم
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
          نظرة عامة على أداء مركز الصيانة
        </Typography>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="إجمالي الطلبات"
              value={totalRepairs}
              icon={<AssignmentIcon sx={{ fontSize: 30 }} />}
              color="#2196F3"
              subtitle="جميع طلبات الصيانة"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="العملاء"
              value={totalCustomers}
              icon={<PeopleIcon sx={{ fontSize: 30 }} />}
              color="#4CAF50"
              subtitle="إجمالي العملاء المسجلين"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="قطع الغيار"
              value={totalSpareParts}
              icon={<BuildIcon sx={{ fontSize: 30 }} />}
              color="#FF9800"
              subtitle="المتوفرة في المخزن"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="إجمالي الديون"
              value={totalDebt}
              icon={<MoneyIcon sx={{ fontSize: 30 }} />}
              color="#F44336"
              subtitle="ريال سعودي"
            />
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          {/* Repair Status Chart */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 400 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  توزيع حالات الطلبات
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Monthly Repairs Chart */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 400 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  الطلبات الشهرية
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="repairs" fill="#2196F3" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Repairs */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 400 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  أحدث الطلبات
                </Typography>
                <List>
                  {recentRepairs.map((repair) => (
                    <ListItem key={repair.id} divider>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: repairStatuses[repair.status]?.color }}>
                          <AssignmentIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={repair.customerName}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {repair.phoneType} - {repair.problem}
                            </Typography>
                            <Chip
                              label={repairStatuses[repair.status]?.label}
                              size="small"
                              sx={{
                                bgcolor: repairStatuses[repair.status]?.color,
                                color: 'white',
                                mt: 0.5
                              }}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Low Stock Alert */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 400 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="warning.main">
                  تنبيه: قطع غيار منخفضة المخزون
                </Typography>
                {lowStockParts.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
                    <Typography variant="h6" color="success.main">
                      جميع قطع الغيار متوفرة بكميات كافية
                    </Typography>
                  </Box>
                ) : (
                  <List>
                    {lowStockParts.map((part) => (
                      <ListItem key={part.id} divider>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'warning.main' }}>
                            <WarningIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={part.name}
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                الكمية المتبقية: {part.quantity}
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={(part.quantity / 10) * 100}
                                color="warning"
                                sx={{ mt: 1 }}
                              />
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Layout>
  );
};

export default Dashboard;
