import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  IconButton,
  Tooltip,
  Alert,
  Snackbar
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { generateRepairReceipt } from '../utils/pdfGenerator';

const ViewRepairs = () => {
  const location = useLocation();
  const { repairs, updateRepair, deleteRepair, repairStatuses } = useData();
  
  const [selectedRepair, setSelectedRepair] = useState(null);
  const [editDialog, setEditDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [statusDialog, setStatusDialog] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  
  const [editData, setEditData] = useState({});
  const [statusUpdate, setStatusUpdate] = useState({
    status: '',
    failureReason: '',
    waitingParts: '',
    deliveryInfo: {
      recipientName: '',
      deliveryDate: '',
      notes: '',
      paymentStatus: 'unpaid',
      paidAmount: 0
    }
  });

  useEffect(() => {
    // Highlight specific repair if coming from search
    if (location.state?.highlightId) {
      const repair = repairs.find(r => r.id === location.state.highlightId);
      if (repair) {
        setSelectedRepair(repair);
      }
    }
  }, [location.state, repairs]);

  const handleEdit = (repair) => {
    setSelectedRepair(repair);
    setEditData({ ...repair });
    setEditDialog(true);
  };

  const handleDelete = (repair) => {
    setSelectedRepair(repair);
    setDeleteDialog(true);
  };

  const handleStatusUpdate = (repair) => {
    setSelectedRepair(repair);
    setStatusUpdate({
      status: repair.status,
      failureReason: repair.failureReason || '',
      waitingParts: repair.waitingParts || '',
      deliveryInfo: repair.deliveryInfo || {
        recipientName: '',
        deliveryDate: '',
        notes: '',
        paymentStatus: 'unpaid',
        paidAmount: 0
      }
    });
    setStatusDialog(true);
  };

  const handlePrint = (repair) => {
    generateRepairReceipt(repair);
  };

  const confirmEdit = () => {
    updateRepair(selectedRepair.id, editData);
    setEditDialog(false);
    setAlert({
      open: true,
      message: 'تم تحديث الطلب بنجاح',
      severity: 'success'
    });
  };

  const confirmDelete = () => {
    deleteRepair(selectedRepair.id);
    setDeleteDialog(false);
    setAlert({
      open: true,
      message: 'تم حذف الطلب بنجاح',
      severity: 'success'
    });
  };

  const confirmStatusUpdate = () => {
    const updates = {
      status: statusUpdate.status,
      lastUpdated: new Date().toISOString()
    };

    if (statusUpdate.status === 'failed') {
      updates.failureReason = statusUpdate.failureReason;
    }

    if (statusUpdate.status === 'waitingParts') {
      updates.waitingParts = statusUpdate.waitingParts;
    }

    if (statusUpdate.status === 'delivered') {
      updates.deliveryInfo = statusUpdate.deliveryInfo;
      
      // Handle payment and debt
      if (statusUpdate.deliveryInfo.paymentStatus === 'partial') {
        const remainingAmount = selectedRepair.price - statusUpdate.deliveryInfo.paidAmount;
        updates.remainingDebt = remainingAmount;
      } else if (statusUpdate.deliveryInfo.paymentStatus === 'unpaid') {
        updates.remainingDebt = selectedRepair.price;
      }
    }

    updateRepair(selectedRepair.id, updates);
    setStatusDialog(false);
    setAlert({
      open: true,
      message: 'تم تحديث حالة الطلب بنجاح',
      severity: 'success'
    });
  };

  const columns = [
    {
      field: 'serialNumber',
      headerName: 'رقم التسلسل',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color="primary"
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'customerName',
      headerName: 'اسم العميل',
      width: 150
    },
    {
      field: 'customerPhone',
      headerName: 'رقم الهاتف',
      width: 120
    },
    {
      field: 'phoneType',
      headerName: 'نوع الهاتف',
      width: 120
    },
    {
      field: 'problem',
      headerName: 'المشكلة',
      width: 200
    },
    {
      field: 'price',
      headerName: 'السعر',
      width: 100,
      renderCell: (params) => `${params.value} ريال`
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={repairStatuses[params.value]?.label || params.value}
          sx={{
            backgroundColor: repairStatuses[params.value]?.color || '#gray',
            color: 'white',
            fontWeight: 'bold'
          }}
          size="small"
        />
      )
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الإنشاء',
      width: 120,
      renderCell: (params) => new Date(params.value).toLocaleDateString('ar')
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="عرض/تحديث الحالة">
            <IconButton
              size="small"
              onClick={() => handleStatusUpdate(params.row)}
              color="primary"
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="تعديل">
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              color="info"
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="طباعة">
            <IconButton
              size="small"
              onClick={() => handlePrint(params.row)}
              color="success"
            >
              <PrintIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف">
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row)}
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  return (
    <Layout>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" color="primary">
          عرض طلبات الصيانة
        </Typography>

        <Card>
          <CardContent>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={repairs}
                columns={columns}
                pageSize={10}
                rowsPerPageOptions={[10, 25, 50]}
                disableSelectionOnClick
                sx={{
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                  },
                  '& .MuiDataGrid-cell': {
                    borderBottom: '1px solid #e0e0e0',
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>تعديل طلب الصيانة</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="اسم العميل"
                  value={editData.customerName || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, customerName: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="رقم الهاتف"
                  value={editData.customerPhone || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, customerPhone: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="نوع الهاتف"
                  value={editData.phoneType || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, phoneType: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="السعر"
                  type="number"
                  value={editData.price || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="المشكلة"
                  multiline
                  rows={3}
                  value={editData.problem || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, problem: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={2}
                  value={editData.notes || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialog(false)}>إلغاء</Button>
            <Button onClick={confirmEdit} variant="contained">حفظ التغييرات</Button>
          </DialogActions>
        </Dialog>

        {/* Status Update Dialog */}
        <Dialog open={statusDialog} onClose={() => setStatusDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>تحديث حالة الطلب</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>الحالة</InputLabel>
                  <Select
                    value={statusUpdate.status}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, status: e.target.value }))}
                    label="الحالة"
                  >
                    {Object.entries(repairStatuses).map(([key, status]) => (
                      <MenuItem key={key} value={key}>
                        {status.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {statusUpdate.status === 'failed' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="سبب الفشل"
                    multiline
                    rows={3}
                    value={statusUpdate.failureReason}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, failureReason: e.target.value }))}
                  />
                </Grid>
              )}

              {statusUpdate.status === 'waitingParts' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="قطع الغيار المطلوبة"
                    multiline
                    rows={3}
                    value={statusUpdate.waitingParts}
                    onChange={(e) => setStatusUpdate(prev => ({ ...prev, waitingParts: e.target.value }))}
                  />
                </Grid>
              )}

              {statusUpdate.status === 'delivered' && (
                <>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="اسم المستلم"
                      value={statusUpdate.deliveryInfo.recipientName}
                      onChange={(e) => setStatusUpdate(prev => ({
                        ...prev,
                        deliveryInfo: { ...prev.deliveryInfo, recipientName: e.target.value }
                      }))}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="تاريخ التسليم"
                      type="date"
                      InputLabelProps={{ shrink: true }}
                      value={statusUpdate.deliveryInfo.deliveryDate}
                      onChange={(e) => setStatusUpdate(prev => ({
                        ...prev,
                        deliveryInfo: { ...prev.deliveryInfo, deliveryDate: e.target.value }
                      }))}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>حالة الدفع</InputLabel>
                      <Select
                        value={statusUpdate.deliveryInfo.paymentStatus}
                        onChange={(e) => setStatusUpdate(prev => ({
                          ...prev,
                          deliveryInfo: { ...prev.deliveryInfo, paymentStatus: e.target.value }
                        }))}
                        label="حالة الدفع"
                      >
                        <MenuItem value="paid">مدفوع بالكامل</MenuItem>
                        <MenuItem value="partial">مدفوع جزئياً</MenuItem>
                        <MenuItem value="unpaid">غير مدفوع</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  {statusUpdate.deliveryInfo.paymentStatus === 'partial' && (
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="المبلغ المدفوع"
                        type="number"
                        value={statusUpdate.deliveryInfo.paidAmount}
                        onChange={(e) => setStatusUpdate(prev => ({
                          ...prev,
                          deliveryInfo: { ...prev.deliveryInfo, paidAmount: parseFloat(e.target.value) }
                        }))}
                      />
                    </Grid>
                  )}
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="ملاحظات التسليم"
                      multiline
                      rows={2}
                      value={statusUpdate.deliveryInfo.notes}
                      onChange={(e) => setStatusUpdate(prev => ({
                        ...prev,
                        deliveryInfo: { ...prev.deliveryInfo, notes: e.target.value }
                      }))}
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatusDialog(false)}>إلغاء</Button>
            <Button onClick={confirmStatusUpdate} variant="contained">تحديث الحالة</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Dialog */}
        <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
          <DialogTitle>تأكيد الحذف</DialogTitle>
          <DialogContent>
            <Typography>
              هل أنت متأكد من حذف طلب الصيانة للعميل "{selectedRepair?.customerName}"؟
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialog(false)}>إلغاء</Button>
            <Button onClick={confirmDelete} variant="contained" color="error">حذف</Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setAlert(prev => ({ ...prev, open: false }))}
            severity={alert.severity}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default ViewRepairs;
