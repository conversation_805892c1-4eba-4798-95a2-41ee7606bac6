import React, { createContext, useContext, useState, useEffect } from 'react';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const [repairs, setRepairs] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [spareParts, setSpareParts] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [activities, setActivities] = useState([]);
  const [settings, setSettings] = useState({
    centerName: 'مركز صيانة الموبايلات',
    address: 'العنوان',
    phone: '123456789',
    logo: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      whatsapp: ''
    },
    documentSettings: {
      size: 'A5',
      printerType: 'normal'
    },
    colors: {
      primary: '#1976d2',
      secondary: '#dc004e'
    }
  });

  // Phone manufacturers
  const phoneManufacturers = [
    'Samsung', 'Apple', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 
    'OnePlus', 'Nokia', 'Sony', 'LG', 'Motorola', 'Realme', 'أخرى'
  ];

  // Repair statuses
  const repairStatuses = {
    pending: { label: 'قيد الصيانة', color: '#ff9800' },
    completed: { label: 'مكتمل', color: '#2196f3' },
    failed: { label: 'فشل الصيانة', color: '#f44336' },
    waitingParts: { label: 'بانتظار قطع الغيار', color: '#9c27b0' },
    delivered: { label: 'تم التسليم', color: '#4caf50' },
    partialPayment: { label: 'تم التسليم - دفع جزئي', color: '#ff5722' },
    unpaid: { label: 'تم التسليم - غير مدفوع', color: '#795548' }
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    // Load data from localStorage
    const savedRepairs = localStorage.getItem('repairs');
    const savedCustomers = localStorage.getItem('customers');
    const savedSpareParts = localStorage.getItem('spareParts');
    const savedDocuments = localStorage.getItem('documents');
    const savedActivities = localStorage.getItem('activities');
    const savedSettings = localStorage.getItem('settings');

    if (savedRepairs) setRepairs(JSON.parse(savedRepairs));
    if (savedCustomers) setCustomers(JSON.parse(savedCustomers));
    if (savedSpareParts) setSpareParts(JSON.parse(savedSpareParts));
    if (savedDocuments) setDocuments(JSON.parse(savedDocuments));
    if (savedActivities) setActivities(JSON.parse(savedActivities));
    if (savedSettings) setSettings({ ...settings, ...JSON.parse(savedSettings) });
  };

  const saveData = (type, data) => {
    localStorage.setItem(type, JSON.stringify(data));
  };

  const addActivity = (activity) => {
    const newActivity = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      ...activity
    };
    const updatedActivities = [newActivity, ...activities];
    setActivities(updatedActivities);
    saveData('activities', updatedActivities);
  };

  // Repair functions
  const addRepair = (repair) => {
    const newRepair = {
      id: Date.now(),
      serialNumber: `R${Date.now()}`,
      status: 'pending',
      createdAt: new Date().toISOString(),
      ...repair
    };
    const updatedRepairs = [...repairs, newRepair];
    setRepairs(updatedRepairs);
    saveData('repairs', updatedRepairs);
    
    addActivity({
      type: 'repair_added',
      description: `تم إضافة طلب صيانة جديد للعميل: ${repair.customerName}`,
      relatedId: newRepair.id
    });
    
    return newRepair;
  };

  const updateRepair = (id, updates) => {
    const updatedRepairs = repairs.map(repair => 
      repair.id === id ? { ...repair, ...updates } : repair
    );
    setRepairs(updatedRepairs);
    saveData('repairs', updatedRepairs);
    
    addActivity({
      type: 'repair_updated',
      description: `تم تحديث طلب الصيانة رقم: ${id}`,
      relatedId: id
    });
  };

  const deleteRepair = (id) => {
    const updatedRepairs = repairs.filter(repair => repair.id !== id);
    setRepairs(updatedRepairs);
    saveData('repairs', updatedRepairs);
    
    addActivity({
      type: 'repair_deleted',
      description: `تم حذف طلب الصيانة رقم: ${id}`,
      relatedId: id
    });
  };

  // Customer functions
  const addCustomer = (customer) => {
    const newCustomer = {
      id: Date.now(),
      createdAt: new Date().toISOString(),
      debt: 0,
      ...customer
    };
    const updatedCustomers = [...customers, newCustomer];
    setCustomers(updatedCustomers);
    saveData('customers', updatedCustomers);

    addActivity({
      type: 'customer_added',
      description: `تم إضافة عميل جديد: ${customer.name}`,
      relatedId: newCustomer.id
    });

    return newCustomer;
  };

  const updateCustomer = (id, updates) => {
    const updatedCustomers = customers.map(customer =>
      customer.id === id ? { ...customer, ...updates } : customer
    );
    setCustomers(updatedCustomers);
    saveData('customers', updatedCustomers);

    addActivity({
      type: 'customer_updated',
      description: `تم تحديث بيانات العميل رقم: ${id}`,
      relatedId: id
    });
  };

  const deleteCustomer = (id) => {
    const updatedCustomers = customers.filter(customer => customer.id !== id);
    setCustomers(updatedCustomers);
    saveData('customers', updatedCustomers);

    addActivity({
      type: 'customer_deleted',
      description: `تم حذف العميل رقم: ${id}`,
      relatedId: id
    });
  };

  // Spare parts functions
  const addSparePart = (part) => {
    const newPart = {
      id: Date.now(),
      createdAt: new Date().toISOString(),
      ...part
    };
    const updatedParts = [...spareParts, newPart];
    setSpareParts(updatedParts);
    saveData('spareParts', updatedParts);

    addActivity({
      type: 'spare_part_added',
      description: `تم إضافة قطعة غيار جديدة: ${part.name}`,
      relatedId: newPart.id
    });

    return newPart;
  };

  const updateSparePart = (id, updates) => {
    const updatedParts = spareParts.map(part =>
      part.id === id ? { ...part, ...updates } : part
    );
    setSpareParts(updatedParts);
    saveData('spareParts', updatedParts);

    addActivity({
      type: 'spare_part_updated',
      description: `تم تحديث قطعة الغيار رقم: ${id}`,
      relatedId: id
    });
  };

  const deleteSparePart = (id) => {
    const updatedParts = spareParts.filter(part => part.id !== id);
    setSpareParts(updatedParts);
    saveData('spareParts', updatedParts);

    addActivity({
      type: 'spare_part_deleted',
      description: `تم حذف قطعة الغيار رقم: ${id}`,
      relatedId: id
    });
  };

  // Search function
  const searchAll = (query) => {
    const results = [];
    const lowerQuery = query.toLowerCase();

    // Search in repairs
    repairs.forEach(repair => {
      if (repair.customerName?.toLowerCase().includes(lowerQuery) ||
          repair.phoneType?.toLowerCase().includes(lowerQuery) ||
          repair.problem?.toLowerCase().includes(lowerQuery) ||
          repair.serialNumber?.toLowerCase().includes(lowerQuery)) {
        results.push({ type: 'repair', data: repair });
      }
    });

    // Search in customers
    customers.forEach(customer => {
      if (customer.name?.toLowerCase().includes(lowerQuery) ||
          customer.phone?.toLowerCase().includes(lowerQuery)) {
        results.push({ type: 'customer', data: customer });
      }
    });

    // Search in spare parts
    spareParts.forEach(part => {
      if (part.name?.toLowerCase().includes(lowerQuery) ||
          part.barcode?.toLowerCase().includes(lowerQuery)) {
        results.push({ type: 'sparePart', data: part });
      }
    });

    return results;
  };

  const value = {
    repairs,
    customers,
    spareParts,
    documents,
    activities,
    settings,
    phoneManufacturers,
    repairStatuses,
    addRepair,
    updateRepair,
    deleteRepair,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    addSparePart,
    updateSparePart,
    deleteSparePart,
    searchAll,
    setSettings,
    saveData
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
