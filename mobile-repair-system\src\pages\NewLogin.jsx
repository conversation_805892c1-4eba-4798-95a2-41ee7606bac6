import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Container,
  InputAdornment,
  IconButton,
  Chip,
  Avatar
} from '@mui/material';
import {
  Person as PersonIcon,
  Lock as LockIcon,
  Visibility,
  VisibilityOff,
  Login as LoginIcon,
  PhoneAndroid as PhoneIcon
} from '@mui/icons-material';
import { useAuth } from '../context/SimpleAuthContext';

const NewLogin = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const result = login(username, password);
      if (result.success) {
        // تسجيل دخول ناجح - الانتقال إلى لوحة التحكم
        navigate('/');
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 25%, #4CAF50 50%, #66BB6A 75%, #81C784 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          animation: 'float 6s ease-in-out infinite',
        }
      }}
    >
      <Container maxWidth="sm">
        <Card 
          sx={{ 
            borderRadius: 6, 
            boxShadow: '0 32px 80px rgba(0,0,0,0.3)',
            background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
            border: '2px solid rgba(255,255,255,0.3)',
            backdropFilter: 'blur(20px)',
            overflow: 'hidden',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '6px',
              background: 'linear-gradient(90deg, #D32F2F 0%, #FFFFFF 25%, #000000 50%, #1B5E20 100%)',
            }
          }}
        >
          <CardContent sx={{ p: 6 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 5 }}>
              <Avatar
                sx={{
                  width: 100,
                  height: 100,
                  mx: 'auto',
                  mb: 3,
                  background: 'linear-gradient(145deg, #1B5E20 0%, #4CAF50 100%)',
                  boxShadow: '0 12px 32px rgba(27, 94, 32, 0.4)',
                  border: '4px solid rgba(255,255,255,0.3)',
                }}
              >
                <PhoneIcon sx={{ fontSize: 50, color: 'white' }} />
              </Avatar>
              
              <Typography 
                variant="h3" 
                component="h1" 
                gutterBottom 
                sx={{ 
                  background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  fontWeight: 'bold',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                🇮🇶 مركز الرافدين
              </Typography>
              
              <Typography variant="h5" color="text.secondary" sx={{ fontWeight: 500 }}>
                نظام إدارة صيانة الهواتف المحمولة
              </Typography>
              
              <Chip
                label="تسجيل الدخول"
                sx={{
                  mt: 2,
                  px: 3,
                  py: 1,
                  fontSize: '1rem',
                  fontWeight: 600,
                  background: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
                  color: 'white',
                  boxShadow: '0 4px 16px rgba(211, 47, 47, 0.3)',
                }}
              />
            </Box>

            {/* Login Form */}
            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="اسم المستخدم"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                margin="normal"
                required
                disabled={loading}
                sx={{ 
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                    boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.1)',
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon sx={{ color: '#1B5E20' }} />
                    </InputAdornment>
                  ),
                  style: { textAlign: 'right', direction: 'rtl', fontSize: '1.1rem' }
                }}
              />

              <TextField
                fullWidth
                label="كلمة المرور"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                margin="normal"
                required
                disabled={loading}
                sx={{ 
                  mb: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
                    boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.1)',
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockIcon sx={{ color: '#1B5E20' }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleTogglePassword}
                        edge="end"
                        disabled={loading}
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                  style: { textAlign: 'right', direction: 'rtl', fontSize: '1.1rem' }
                }}
              />

              {error && (
                <Alert 
                  severity="error" 
                  sx={{ 
                    mb: 3, 
                    textAlign: 'right',
                    borderRadius: 3,
                    boxShadow: '0 4px 16px rgba(211, 47, 47, 0.2)',
                  }}
                >
                  {error}
                </Alert>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                startIcon={<LoginIcon />}
                sx={{
                  py: 2.5,
                  fontSize: '1.3rem',
                  fontWeight: 'bold',
                  borderRadius: 3,
                  background: 'linear-gradient(145deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%)',
                  boxShadow: '0 8px 24px rgba(27, 94, 32, 0.4)',
                  border: '2px solid rgba(255,255,255,0.2)',
                  '&:hover': {
                    background: 'linear-gradient(145deg, #0D4E14 0%, #1B5E20 50%, #2E7D32 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 32px rgba(27, 94, 32, 0.5)',
                  },
                  '&:disabled': {
                    background: 'linear-gradient(145deg, #9E9E9E 0%, #BDBDBD 100%)',
                  }
                }}
              >
                {loading ? 'جاري تسجيل الدخول...' : 'دخول إلى النظام'}
              </Button>
            </form>

            {/* Demo Credentials */}
            <Box 
              sx={{ 
                mt: 4, 
                p: 3, 
                backgroundColor: 'rgba(27, 94, 32, 0.1)', 
                borderRadius: 3,
                border: '2px solid rgba(27, 94, 32, 0.2)',
                textAlign: 'center'
              }}
            >
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600, mb: 1 }}>
                🔐 معلومات تسجيل الدخول:
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#1B5E20' }}>
                اسم المستخدم: <span style={{ color: '#D32F2F' }}>abd</span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#1B5E20' }}>
                كلمة المرور: <span style={{ color: '#D32F2F' }}>ZAin1998</span>
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Container>

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
      `}</style>
    </Box>
  );
};

export default NewLogin;
