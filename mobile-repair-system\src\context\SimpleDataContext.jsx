import React, { createContext, useContext, useState, useEffect } from 'react';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  // State for all data
  const [repairs, setRepairs] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [spareParts, setSpareParts] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [activityLog, setActivityLog] = useState([]);
  const [settings, setSettings] = useState({
    centerName: 'مركز الرافدين لصيانة الهواتف المحمولة',
    address: 'بغداد - الكرادة - شارع الرئيسي',
    phone: '07901234567',
    email: '<EMAIL>',
    logo: '',
    currency: 'د.ع',
    printSize: 'A5',
    socialMedia: {
      facebook: 'RafidainMobile',
      instagram: 'rafidain_mobile',
      whatsapp: '07901234567',
      telegram: '@rafidain_mobile'
    },
    colors: {
      primary: '#1B5E20',
      secondary: '#D32F2F',
      success: '#2E7D32',
      warning: '#F57C00',
      error: '#D32F2F',
      info: '#1976D2'
    }
  });

  // Repair statuses
  const repairStatuses = {
    pending: { label: 'قيد الصيانة', color: '#F57C00' },
    completed: { label: 'مكتمل', color: '#1976D2' },
    delivered: { label: 'تم التسليم', color: '#2E7D32' },
    failed: { label: 'فشل الصيانة', color: '#D32F2F' },
    waiting_parts: { label: 'بانتظار قطع الغيار', color: '#9C27B0' },
    partial_payment: { label: 'دفع جزئي', color: '#FF9800' },
    unpaid: { label: 'لم يدفع', color: '#795548' }
  };

  // Phone brands
  const phoneBrands = [
    'Samsung', 'Apple', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 
    'OnePlus', 'Nokia', 'Sony', 'LG', 'Motorola', 'Realme', 'Honor'
  ];

  // Load data from localStorage on mount
  useEffect(() => {
    const savedRepairs = localStorage.getItem('repairs');
    const savedCustomers = localStorage.getItem('customers');
    const savedSpareParts = localStorage.getItem('spareParts');
    const savedDocuments = localStorage.getItem('documents');
    const savedActivityLog = localStorage.getItem('activityLog');
    const savedSettings = localStorage.getItem('settings');

    if (savedRepairs) setRepairs(JSON.parse(savedRepairs));
    if (savedCustomers) setCustomers(JSON.parse(savedCustomers));
    if (savedSpareParts) setSpareParts(JSON.parse(savedSpareParts));
    if (savedDocuments) setDocuments(JSON.parse(savedDocuments));
    if (savedActivityLog) setActivityLog(JSON.parse(savedActivityLog));
    if (savedSettings) setSettings(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('repairs', JSON.stringify(repairs));
  }, [repairs]);

  useEffect(() => {
    localStorage.setItem('customers', JSON.stringify(customers));
  }, [customers]);

  useEffect(() => {
    localStorage.setItem('spareParts', JSON.stringify(spareParts));
  }, [spareParts]);

  useEffect(() => {
    localStorage.setItem('documents', JSON.stringify(documents));
  }, [documents]);

  useEffect(() => {
    localStorage.setItem('activityLog', JSON.stringify(activityLog));
  }, [activityLog]);

  useEffect(() => {
    localStorage.setItem('settings', JSON.stringify(settings));
  }, [settings]);

  // Helper functions
  const addActivity = (activity) => {
    const newActivity = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      ...activity
    };
    setActivityLog(prev => [newActivity, ...prev]);
  };

  const generateSerialNumber = (prefix = 'R') => {
    return `${prefix}${Date.now()}`;
  };

  // Search function
  const searchAll = (query) => {
    const results = [];
    const searchTerm = query.toLowerCase();

    // Search in repairs
    repairs.forEach(repair => {
      if (
        repair.serialNumber?.toLowerCase().includes(searchTerm) ||
        repair.customerName?.toLowerCase().includes(searchTerm) ||
        repair.phoneType?.toLowerCase().includes(searchTerm) ||
        repair.problem?.toLowerCase().includes(searchTerm)
      ) {
        results.push({ type: 'repair', data: repair });
      }
    });

    // Search in customers
    customers.forEach(customer => {
      if (
        customer.name?.toLowerCase().includes(searchTerm) ||
        customer.phone?.toLowerCase().includes(searchTerm) ||
        customer.address?.toLowerCase().includes(searchTerm)
      ) {
        results.push({ type: 'customer', data: customer });
      }
    });

    // Search in spare parts
    spareParts.forEach(part => {
      if (
        part.name?.toLowerCase().includes(searchTerm) ||
        part.barcode?.toLowerCase().includes(searchTerm)
      ) {
        results.push({ type: 'sparePart', data: part });
      }
    });

    return results;
  };

  const value = {
    // Data
    repairs,
    customers,
    spareParts,
    documents,
    activityLog,
    settings,
    
    // Setters
    setRepairs,
    setCustomers,
    setSpareParts,
    setDocuments,
    setActivityLog,
    setSettings,
    
    // Constants
    repairStatuses,
    phoneBrands,
    
    // Helper functions
    addActivity,
    generateSerialNumber,
    searchAll
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
