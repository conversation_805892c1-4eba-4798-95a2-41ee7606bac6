import React, { useState } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  InputAdornment,
  Paper
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Payment as PaymentIcon,
  Build as BuildIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';

const ActivityLog = () => {
  const { activities } = useData();
  
  const [filters, setFilters] = useState({
    type: 'all',
    dateFrom: null,
    dateTo: null,
    search: ''
  });

  // Activity types and their configurations
  const activityTypes = {
    login: { label: 'تسجيل دخول', icon: <LoginIcon />, color: 'success' },
    logout: { label: 'تسجيل خروج', icon: <LogoutIcon />, color: 'info' },
    repair_added: { label: 'إضافة طلب صيانة', icon: <AddIcon />, color: 'primary' },
    repair_updated: { label: 'تحديث طلب صيانة', icon: <EditIcon />, color: 'warning' },
    repair_deleted: { label: 'حذف طلب صيانة', icon: <DeleteIcon />, color: 'error' },
    customer_added: { label: 'إضافة عميل', icon: <PersonIcon />, color: 'success' },
    customer_updated: { label: 'تحديث عميل', icon: <EditIcon />, color: 'warning' },
    spare_part_added: { label: 'إضافة قطعة غيار', icon: <BuildIcon />, color: 'primary' },
    payment_received: { label: 'استلام دفعة', icon: <PaymentIcon />, color: 'success' },
    system_backup: { label: 'نسخ احتياطي', icon: <HistoryIcon />, color: 'info' },
    settings_updated: { label: 'تحديث الإعدادات', icon: <EditIcon />, color: 'warning' }
  };

  // Filter activities
  const filteredActivities = activities.filter(activity => {
    // Filter by type
    if (filters.type !== 'all' && activity.type !== filters.type) {
      return false;
    }

    // Filter by date range
    if (filters.dateFrom && dayjs(activity.timestamp).isBefore(filters.dateFrom, 'day')) {
      return false;
    }
    if (filters.dateTo && dayjs(activity.timestamp).isAfter(filters.dateTo, 'day')) {
      return false;
    }

    // Filter by search
    if (filters.search && !activity.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    return true;
  });

  const getActivityIcon = (type) => {
    return activityTypes[type]?.icon || <AssignmentIcon />;
  };

  const getActivityColor = (type) => {
    return activityTypes[type]?.color || 'default';
  };

  const getActivityLabel = (type) => {
    return activityTypes[type]?.label || type;
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('ar'),
      time: date.toLocaleTimeString('ar', { hour: '2-digit', minute: '2-digit' })
    };
  };

  // Group activities by date
  const groupedActivities = filteredActivities.reduce((groups, activity) => {
    const date = new Date(activity.timestamp).toLocaleDateString('ar');
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(activity);
    return groups;
  }, {});

  return (
    <Layout>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" color="primary">
          سجل العمليات
        </Typography>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>نوع العملية</InputLabel>
                  <Select
                    value={filters.type}
                    onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                    label="نوع العملية"
                  >
                    <MenuItem value="all">جميع العمليات</MenuItem>
                    {Object.entries(activityTypes).map(([key, config]) => (
                      <MenuItem key={key} value={key}>
                        {config.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="من تاريخ"
                  value={filters.dateFrom}
                  onChange={(newValue) => setFilters(prev => ({ ...prev, dateFrom: newValue }))}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="إلى تاريخ"
                  value={filters.dateTo}
                  onChange={(newValue) => setFilters(prev => ({ ...prev, dateTo: newValue }))}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="البحث في السجل"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" fontWeight="bold">
                  {filteredActivities.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إجمالي العمليات
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {filteredActivities.filter(a => a.type.includes('added')).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  عمليات الإضافة
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {filteredActivities.filter(a => a.type.includes('updated')).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  عمليات التحديث
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main" fontWeight="bold">
                  {filteredActivities.filter(a => a.type.includes('deleted')).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  عمليات الحذف
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Activities List */}
        <Card>
          <CardContent>
            {Object.keys(groupedActivities).length === 0 ? (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <HistoryIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary">
                  لا توجد عمليات في الفترة المحددة
                </Typography>
              </Paper>
            ) : (
              Object.entries(groupedActivities)
                .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                .map(([date, dayActivities]) => (
                  <Box key={date} sx={{ mb: 3 }}>
                    <Typography variant="h6" color="primary" gutterBottom>
                      {date}
                    </Typography>
                    <List>
                      {dayActivities
                        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                        .map((activity, index) => {
                          const { time } = formatTimestamp(activity.timestamp);
                          return (
                            <React.Fragment key={activity.id}>
                              <ListItem alignItems="flex-start">
                                <ListItemAvatar>
                                  <Avatar
                                    sx={{
                                      bgcolor: `${getActivityColor(activity.type)}.main`,
                                      width: 40,
                                      height: 40
                                    }}
                                  >
                                    {getActivityIcon(activity.type)}
                                  </Avatar>
                                </ListItemAvatar>
                                <ListItemText
                                  primary={
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                      <Typography variant="body1" fontWeight="medium">
                                        {activity.description}
                                      </Typography>
                                      <Chip
                                        label={getActivityLabel(activity.type)}
                                        color={getActivityColor(activity.type)}
                                        size="small"
                                        variant="outlined"
                                      />
                                    </Box>
                                  }
                                  secondary={
                                    <Box sx={{ mt: 1 }}>
                                      <Typography variant="body2" color="text.secondary">
                                        الوقت: {time}
                                      </Typography>
                                      {activity.user && (
                                        <Typography variant="body2" color="text.secondary">
                                          المستخدم: {activity.user}
                                        </Typography>
                                      )}
                                      {activity.relatedId && (
                                        <Typography variant="body2" color="text.secondary">
                                          معرف العنصر: {activity.relatedId}
                                        </Typography>
                                      )}
                                    </Box>
                                  }
                                />
                              </ListItem>
                              {index < dayActivities.length - 1 && <Divider variant="inset" component="li" />}
                            </React.Fragment>
                          );
                        })}
                    </List>
                  </Box>
                ))
            )}
          </CardContent>
        </Card>
      </Box>
    </Layout>
  );
};

export default ActivityLog;
