import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Chip,
  IconButton,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Print as PrintIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { generateRepairReceipt, generatePaymentReceipt } from '../utils/pdfGenerator';

const Documents = () => {
  const { repairs, customers, documents } = useData();
  
  const [filters, setFilters] = useState({
    type: 'all',
    dateFrom: null,
    dateTo: null,
    customer: 'all'
  });

  // Combine all documents (repairs and payments)
  const getAllDocuments = () => {
    const allDocs = [];

    // Add repair receipts
    repairs.forEach(repair => {
      allDocs.push({
        id: `repair-${repair.id}`,
        type: 'repair',
        title: `إيصال صيانة - ${repair.serialNumber}`,
        customerName: repair.customerName,
        amount: repair.price,
        date: repair.createdAt,
        data: repair
      });
    });

    // Add payment receipts from documents array
    documents.filter(doc => doc.type === 'payment').forEach(doc => {
      allDocs.push({
        id: `payment-${doc.id}`,
        type: 'payment',
        title: `إيصال تسديد - ${doc.id}`,
        customerName: doc.customerName,
        amount: doc.amount,
        date: doc.createdAt,
        data: doc
      });
    });

    return allDocs.sort((a, b) => new Date(b.date) - new Date(a.date));
  };

  const filteredDocuments = getAllDocuments().filter(doc => {
    // Filter by type
    if (filters.type !== 'all' && doc.type !== filters.type) {
      return false;
    }

    // Filter by date range
    if (filters.dateFrom && dayjs(doc.date).isBefore(filters.dateFrom, 'day')) {
      return false;
    }
    if (filters.dateTo && dayjs(doc.date).isAfter(filters.dateTo, 'day')) {
      return false;
    }

    // Filter by customer
    if (filters.customer !== 'all' && doc.customerName !== filters.customer) {
      return false;
    }

    return true;
  });

  const handlePrint = (document) => {
    if (document.type === 'repair') {
      generateRepairReceipt(document.data);
    } else if (document.type === 'payment') {
      const customer = customers.find(c => c.name === document.customerName);
      generatePaymentReceipt(document.data, customer);
    }
  };

  const handleView = (document) => {
    // For now, just print the document
    handlePrint(document);
  };

  const getDocumentTypeLabel = (type) => {
    switch (type) {
      case 'repair':
        return 'إيصال صيانة';
      case 'payment':
        return 'إيصال تسديد';
      default:
        return type;
    }
  };

  const getDocumentTypeColor = (type) => {
    switch (type) {
      case 'repair':
        return 'primary';
      case 'payment':
        return 'success';
      default:
        return 'default';
    }
  };

  const columns = [
    {
      field: 'title',
      headerName: 'عنوان المستند',
      width: 250
    },
    {
      field: 'type',
      headerName: 'نوع المستند',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={getDocumentTypeLabel(params.value)}
          color={getDocumentTypeColor(params.value)}
          variant="filled"
          size="small"
        />
      )
    },
    {
      field: 'customerName',
      headerName: 'اسم العميل',
      width: 200
    },
    {
      field: 'amount',
      headerName: 'المبلغ',
      width: 120,
      renderCell: (params) => `${params.value} ريال`
    },
    {
      field: 'date',
      headerName: 'التاريخ',
      width: 150,
      renderCell: (params) => new Date(params.value).toLocaleDateString('ar')
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="عرض">
            <IconButton
              size="small"
              onClick={() => handleView(params.row)}
              color="primary"
            >
              <ViewIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="طباعة">
            <IconButton
              size="small"
              onClick={() => handlePrint(params.row)}
              color="success"
            >
              <PrintIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="تحميل">
            <IconButton
              size="small"
              onClick={() => handlePrint(params.row)}
              color="info"
            >
              <DownloadIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  const uniqueCustomers = [...new Set(getAllDocuments().map(doc => doc.customerName))];

  return (
    <Layout>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" color="primary">
          إدارة المستندات
        </Typography>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <FilterIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              فلترة المستندات
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>نوع المستند</InputLabel>
                  <Select
                    value={filters.type}
                    onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                    label="نوع المستند"
                  >
                    <MenuItem value="all">جميع الأنواع</MenuItem>
                    <MenuItem value="repair">إيصالات الصيانة</MenuItem>
                    <MenuItem value="payment">إيصالات التسديد</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>العميل</InputLabel>
                  <Select
                    value={filters.customer}
                    onChange={(e) => setFilters(prev => ({ ...prev, customer: e.target.value }))}
                    label="العميل"
                  >
                    <MenuItem value="all">جميع العملاء</MenuItem>
                    {uniqueCustomers.map(customer => (
                      <MenuItem key={customer} value={customer}>
                        {customer}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="من تاريخ"
                  value={filters.dateFrom}
                  onChange={(newValue) => setFilters(prev => ({ ...prev, dateFrom: newValue }))}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <DatePicker
                  label="إلى تاريخ"
                  value={filters.dateTo}
                  onChange={(newValue) => setFilters(prev => ({ ...prev, dateTo: newValue }))}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" fontWeight="bold">
                  {filteredDocuments.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إجمالي المستندات
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" fontWeight="bold">
                  {filteredDocuments.filter(doc => doc.type === 'repair').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إيصالات الصيانة
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {filteredDocuments.filter(doc => doc.type === 'payment').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إيصالات التسديد
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {filteredDocuments.reduce((sum, doc) => sum + doc.amount, 0).toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إجمالي المبالغ (ريال)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Documents Table */}
        <Card>
          <CardContent>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={filteredDocuments}
                columns={columns}
                pageSize={10}
                rowsPerPageOptions={[10, 25, 50]}
                disableSelectionOnClick
                sx={{
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Layout>
  );
};

export default Documents;
