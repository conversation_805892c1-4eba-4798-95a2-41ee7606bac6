import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Button,
  Avatar,
  Chip,
  AppBar,
  Toolbar,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Dashboard as DashboardIcon,
  Add as AddIcon,
  List as ListIcon,
  People as PeopleIcon,
  Build as BuildIcon,
  Description as DocumentIcon,
  Assessment as ReportIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Phone as PhoneIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useAuth } from '../context/SimpleAuthContext';
import { useData } from '../context/SimpleDataContext';

const SimpleDashboard = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(true);
  const { user, logout } = useAuth();
  const { repairs, customers, spareParts, searchAll } = useData();

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    if (e.target.value.trim()) {
      const results = searchAll(e.target.value);
      console.log('نتائج البحث:', results);
    }
  };

  const handleLogout = () => {
    logout();
  };

  // Statistics
  const stats = {
    totalRepairs: repairs.length,
    totalCustomers: customers.length,
    totalSpareParts: spareParts.length,
    totalRevenue: repairs.reduce((sum, repair) => sum + (repair.price || 0), 0)
  };

  const menuItems = [
    { text: 'لوحة التحكم', icon: <DashboardIcon />, path: '/' },
    { text: 'إضافة صيانة', icon: <AddIcon />, path: '/add-repair' },
    { text: 'عرض الطلبات', icon: <ListIcon />, path: '/view-repairs' },
    { text: 'العملاء', icon: <PeopleIcon />, path: '/customers' },
    { text: 'قطع الغيار', icon: <BuildIcon />, path: '/spare-parts' },
    { text: 'المستندات', icon: <DocumentIcon />, path: '/documents' },
    { text: 'التقارير', icon: <ReportIcon />, path: '/reports' },
    { text: 'السجل', icon: <HistoryIcon />, path: '/activity-log' },
    { text: 'الإعدادات', icon: <SettingsIcon />, path: '/settings' },
  ];

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar 
        position="fixed" 
        sx={{ 
          zIndex: (theme) => theme.zIndex.drawer + 1,
          background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
          boxShadow: '0 4px 20px rgba(27, 94, 32, 0.3)'
        }}
      >
        <Toolbar>
          <PhoneIcon sx={{ mr: 2, fontSize: 32 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            🇮🇶 مركز الرافدين لصيانة الهواتف المحمولة
          </Typography>
          
          {/* Search Bar */}
          <TextField
            placeholder="🔍 البحث في النظام..."
            value={searchQuery}
            onChange={handleSearch}
            size="small"
            sx={{
              width: 300,
              mr: 2,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255,255,255,0.15)',
                borderRadius: 3,
                '& fieldset': { borderColor: 'rgba(255,255,255,0.3)' },
                '&:hover fieldset': { borderColor: 'rgba(255,255,255,0.5)' },
                '&.Mui-focused fieldset': { borderColor: 'white' },
                '& input': { color: 'white', textAlign: 'right' }
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: 'rgba(255,255,255,0.7)' }} />
                </InputAdornment>
              ),
            }}
          />
          
          <Avatar sx={{ bgcolor: '#D32F2F', mr: 1 }}>
            {user?.name?.charAt(0) || 'ع'}
          </Avatar>
          <Typography variant="body2" sx={{ mr: 2 }}>
            مرحباً، {user?.name || 'المستخدم'}
          </Typography>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant="permanent"
        sx={{
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            background: 'linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRight: '3px solid #1B5E20',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto', p: 2 }}>
          <List>
            {menuItems.map((item, index) => (
              <ListItem 
                button 
                key={index}
                sx={{
                  mb: 1,
                  borderRadius: 3,
                  background: 'linear-gradient(145deg, #ffffff 0%, #f0f0f0 100%)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  border: '2px solid transparent',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'linear-gradient(145deg, #1B5E20 0%, #4CAF50 100%)',
                    color: 'white',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 8px 20px rgba(27, 94, 32, 0.3)',
                    '& .MuiListItemIcon-root': { color: 'white' },
                  }
                }}
              >
                <ListItemIcon sx={{ color: '#1B5E20' }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text} 
                  sx={{ 
                    textAlign: 'right',
                    '& .MuiTypography-root': { fontWeight: 600 }
                  }} 
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        
        <Container maxWidth="xl">
          {/* Welcome Section */}
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography variant="h3" component="h1" gutterBottom sx={{ 
              background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold'
            }}>
              🇮🇶 مرحباً بك في نظام الرافدين
            </Typography>
            <Typography variant="h6" color="text.secondary">
              نظام إدارة شامل لمراكز صيانة الهواتف المحمولة
            </Typography>
            <Chip
              label={`اليوم: ${new Date().toLocaleDateString('ar-IQ', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}`}
              sx={{
                mt: 2,
                px: 3,
                py: 1,
                fontSize: '1rem',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
                color: 'white',
                boxShadow: '0 4px 16px rgba(211, 47, 47, 0.3)',
              }}
            />
          </Box>

          {/* Statistics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 24px rgba(27, 94, 32, 0.3)'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <ListIcon sx={{ fontSize: 48, mb: 1 }} />
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {stats.totalRepairs}
                  </Typography>
                  <Typography variant="body1">
                    إجمالي الطلبات
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                background: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 24px rgba(211, 47, 47, 0.3)'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <PeopleIcon sx={{ fontSize: 48, mb: 1 }} />
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {stats.totalCustomers}
                  </Typography>
                  <Typography variant="body1">
                    إجمالي العملاء
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                background: 'linear-gradient(135deg, #1976D2 0%, #2196F3 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 24px rgba(25, 118, 210, 0.3)'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <BuildIcon sx={{ fontSize: 48, mb: 1 }} />
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {stats.totalSpareParts}
                  </Typography>
                  <Typography variant="body1">
                    قطع الغيار
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                background: 'linear-gradient(135deg, #F57C00 0%, #FF9800 100%)',
                color: 'white',
                borderRadius: 4,
                boxShadow: '0 8px 24px rgba(245, 124, 0, 0.3)'
              }}>
                <CardContent sx={{ textAlign: 'center' }}>
                  <MoneyIcon sx={{ fontSize: 48, mb: 1 }} />
                  <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
                    {stats.totalRevenue.toLocaleString()}
                  </Typography>
                  <Typography variant="body1">
                    الإيرادات (د.ع)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Quick Actions */}
          <Card sx={{ borderRadius: 4, boxShadow: '0 8px 24px rgba(0,0,0,0.1)' }}>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom sx={{ 
                textAlign: 'center',
                fontWeight: 'bold',
                color: '#1B5E20',
                mb: 3
              }}>
                🚀 الإجراءات السريعة
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<AddIcon />}
                    sx={{
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                    }}
                  >
                    إضافة طلب صيانة
                  </Button>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<PeopleIcon />}
                    sx={{
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #D32F2F 0%, #F44336 100%)',
                    }}
                  >
                    إدارة العملاء
                  </Button>
                </Grid>
                
                <Grid item xs={12} sm={6} md={4}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<ReportIcon />}
                    sx={{
                      py: 2,
                      fontSize: '1.1rem',
                      fontWeight: 'bold',
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #1976D2 0%, #2196F3 100%)',
                    }}
                  >
                    عرض التقارير
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Container>
      </Box>
    </Box>
  );
};

export default SimpleDashboard;
