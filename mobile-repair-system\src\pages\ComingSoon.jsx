import React from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Button,
  Avatar
} from '@mui/material';
import { ArrowBack as BackIcon, Build as BuildIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const ComingSoon = ({ title, description, icon }) => {
  const navigate = useNavigate();

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)', 
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <Container maxWidth="sm">
        <Card sx={{ 
          borderRadius: 4, 
          boxShadow: '0 12px 40px rgba(0,0,0,0.1)',
          textAlign: 'center'
        }}>
          <CardContent sx={{ p: 6 }}>
            <Avatar
              sx={{
                width: 100,
                height: 100,
                mx: 'auto',
                mb: 3,
                background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                boxShadow: '0 8px 24px rgba(27, 94, 32, 0.3)',
              }}
            >
              {icon || <BuildIcon sx={{ fontSize: 50 }} />}
            </Avatar>
            
            <Typography variant="h4" component="h1" gutterBottom sx={{ 
              background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold'
            }}>
              {title || 'قريباً...'}
            </Typography>
            
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              {description || 'هذا القسم قيد التطوير وسيكون متاحاً قريباً'}
            </Typography>
            
            <Typography variant="body1" sx={{ mb: 4, color: '#666' }}>
              نعمل بجد لإنجاز هذا القسم وتقديم أفضل تجربة لك.
              شكراً لصبرك! 🚀
            </Typography>
            
            <Button
              variant="contained"
              startIcon={<BackIcon />}
              onClick={() => navigate('/')}
              sx={{
                py: 2,
                px: 4,
                fontSize: '1.1rem',
                fontWeight: 'bold',
                borderRadius: 3,
                background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                boxShadow: '0 8px 24px rgba(27, 94, 32, 0.3)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0D4E14 0%, #2E7D32 100%)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 12px 32px rgba(27, 94, 32, 0.4)',
                }
              }}
            >
              العودة إلى لوحة التحكم
            </Button>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default ComingSoon;
