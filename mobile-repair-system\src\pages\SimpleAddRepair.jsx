import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Alert
} from '@mui/material';
import { Add as AddIcon, ArrowBack as BackIcon } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useData } from '../context/SimpleDataContext';

const SimpleAddRepair = () => {
  const navigate = useNavigate();
  const { customers, phoneBrands, addActivity, generateSerialNumber, setRepairs } = useData();
  
  const [formData, setFormData] = useState({
    customerName: '',
    customerPhone: '',
    phoneType: '',
    problem: '',
    price: '',
    paymentType: 'cash',
    partialAmount: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });
  
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    if (!formData.customerName || !formData.customerPhone || !formData.phoneType || !formData.problem || !formData.price) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // Create new repair
    const newRepair = {
      id: Date.now(),
      serialNumber: generateSerialNumber('R'),
      ...formData,
      price: parseFloat(formData.price),
      partialAmount: formData.paymentType === 'partial' ? parseFloat(formData.partialAmount || 0) : 0,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    // Add to repairs
    setRepairs(prev => [...prev, newRepair]);

    // Add activity log
    addActivity({
      type: 'repair_added',
      description: `تم إضافة طلب صيانة جديد للعميل ${formData.customerName}`,
      repairId: newRepair.id
    });

    setSuccess('تم إضافة طلب الصيانة بنجاح!');
    
    // Reset form
    setFormData({
      customerName: '',
      customerPhone: '',
      phoneType: '',
      problem: '',
      price: '',
      paymentType: 'cash',
      partialAmount: '',
      date: new Date().toISOString().split('T')[0],
      notes: ''
    });
  };

  return (
    <Box sx={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)', py: 4 }}>
      <Container maxWidth="md">
        {/* Header */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Button
            startIcon={<BackIcon />}
            onClick={() => navigate('/')}
            sx={{ mb: 2, alignSelf: 'flex-start' }}
          >
            العودة إلى لوحة التحكم
          </Button>
          
          <Typography variant="h3" component="h1" gutterBottom sx={{ 
            background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold'
          }}>
            ➕ إضافة طلب صيانة جديد
          </Typography>
        </Box>

        {/* Form Card */}
        <Card sx={{ borderRadius: 4, boxShadow: '0 12px 40px rgba(0,0,0,0.1)' }}>
          <CardContent sx={{ p: 4 }}>
            {success && (
              <Alert severity="success" sx={{ mb: 3, borderRadius: 3 }}>
                {success}
              </Alert>
            )}
            
            {error && (
              <Alert severity="error" sx={{ mb: 3, borderRadius: 3 }}>
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Customer Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ color: '#1B5E20', fontWeight: 'bold', mb: 2 }}>
                    👤 معلومات العميل
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="اسم العميل *"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    required
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="رقم الهاتف *"
                    value={formData.customerPhone}
                    onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                    required
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                {/* Device Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ color: '#1B5E20', fontWeight: 'bold', mb: 2, mt: 2 }}>
                    📱 معلومات الجهاز
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>نوع الهاتف *</InputLabel>
                    <Select
                      value={formData.phoneType}
                      onChange={(e) => handleInputChange('phoneType', e.target.value)}
                      label="نوع الهاتف *"
                      sx={{ borderRadius: 3 }}
                    >
                      {phoneBrands.map((brand) => (
                        <MenuItem key={brand} value={brand}>
                          {brand}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="المشكلة (العطل) *"
                    value={formData.problem}
                    onChange={(e) => handleInputChange('problem', e.target.value)}
                    required
                    multiline
                    rows={2}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                {/* Financial Information */}
                <Grid item xs={12}>
                  <Typography variant="h6" sx={{ color: '#1B5E20', fontWeight: 'bold', mb: 2, mt: 2 }}>
                    💰 المعلومات المالية
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="سعر الصيانة (د.ع) *"
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    required
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>طريقة الدفع</InputLabel>
                    <Select
                      value={formData.paymentType}
                      onChange={(e) => handleInputChange('paymentType', e.target.value)}
                      label="طريقة الدفع"
                      sx={{ borderRadius: 3 }}
                    >
                      <MenuItem value="cash">نقداً</MenuItem>
                      <MenuItem value="partial">دفع جزئي</MenuItem>
                      <MenuItem value="deferred">آجل بعد الصيانة</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {formData.paymentType === 'partial' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="المبلغ المدفوع (د.ع)"
                      type="number"
                      value={formData.partialAmount}
                      onChange={(e) => handleInputChange('partialAmount', e.target.value)}
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                    />
                  </Grid>
                )}

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="التاريخ"
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="ملاحظات إضافية"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    multiline
                    rows={3}
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 3 } }}
                  />
                </Grid>

                {/* Submit Button */}
                <Grid item xs={12}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    startIcon={<AddIcon />}
                    sx={{
                      py: 2,
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #1B5E20 0%, #4CAF50 100%)',
                      boxShadow: '0 8px 24px rgba(27, 94, 32, 0.3)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #0D4E14 0%, #2E7D32 100%)',
                        transform: 'translateY(-2px)',
                        boxShadow: '0 12px 32px rgba(27, 94, 32, 0.4)',
                      }
                    }}
                  >
                    إضافة طلب الصيانة
                  </Button>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default SimpleAddRepair;
