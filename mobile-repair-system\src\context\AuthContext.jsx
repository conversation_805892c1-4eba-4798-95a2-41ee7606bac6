import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Default credentials
  const DEFAULT_USERNAME = 'abd';
  const DEFAULT_PASSWORD = 'ZAin1998';

  useEffect(() => {
    // Check if user is already logged in
    const savedAuth = localStorage.getItem('isAuthenticated');
    const savedUser = localStorage.getItem('user');
    
    if (savedAuth === 'true' && savedUser) {
      setIsAuthenticated(true);
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    try {
      // Debug logging
      console.log('Login attempt:', { username, password });
      console.log('Expected:', { DEFAULT_USERNAME, DEFAULT_PASSWORD });
      console.log('Username match:', username === DEFAULT_USERNAME);
      console.log('Password match:', password === DEFAULT_PASSWORD);

      // Simple authentication check
      if (username === DEFAULT_USERNAME && password === DEFAULT_PASSWORD) {
        const userData = {
          username: username,
          loginTime: new Date().toISOString(),
        };

        setIsAuthenticated(true);
        setUser(userData);

        // Save to localStorage
        localStorage.setItem('isAuthenticated', 'true');
        localStorage.setItem('user', JSON.stringify(userData));

        // Log the login activity
        const loginActivity = {
          id: Date.now(),
          type: 'login',
          description: `تسجيل دخول المستخدم: ${username}`,
          timestamp: new Date().toISOString(),
          user: username
        };

        // Save login activity to localStorage
        const activities = JSON.parse(localStorage.getItem('activities') || '[]');
        activities.unshift(loginActivity);
        localStorage.setItem('activities', JSON.stringify(activities));

        return { success: true };
      } else {
        console.log('Login failed - credentials do not match');
        return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
    }
  };

  const logout = () => {
    // Log the logout activity
    const logoutActivity = {
      id: Date.now(),
      type: 'logout',
      description: `تسجيل خروج المستخدم: ${user?.username}`,
      timestamp: new Date().toISOString(),
      user: user?.username
    };
    
    // Save logout activity to localStorage
    const activities = JSON.parse(localStorage.getItem('activities') || '[]');
    activities.unshift(logoutActivity);
    localStorage.setItem('activities', JSON.stringify(activities));
    
    setIsAuthenticated(false);
    setUser(null);
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('user');
  };

  const value = {
    isAuthenticated,
    user,
    loading,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
