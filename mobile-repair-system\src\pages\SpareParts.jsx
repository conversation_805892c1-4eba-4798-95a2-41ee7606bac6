import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  IconButton,
  Tooltip,
  Alert,
  Snackbar,
  Chip
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  FileUpload as ImportIcon,
  FileDownload as ExportIcon
} from '@mui/icons-material';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { exportToPDF } from '../utils/pdfGenerator';

const SpareParts = () => {
  const location = useLocation();
  const { spareParts, addSparePart, updateSparePart, deleteSparePart } = useData();
  
  const [selectedPart, setSelectedPart] = useState(null);
  const [addDialog, setAddDialog] = useState(false);
  const [editDialog, setEditDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  
  const [newPart, setNewPart] = useState({
    name: '',
    purchasePrice: '',
    salePrice: '',
    quantity: '',
    barcode: '',
    notes: ''
  });

  const [editData, setEditData] = useState({});

  useEffect(() => {
    // Highlight specific part if coming from search
    if (location.state?.highlightId) {
      const part = spareParts.find(p => p.id === location.state.highlightId);
      if (part) {
        setSelectedPart(part);
      }
    }
  }, [location.state, spareParts]);

  const handleAdd = () => {
    setNewPart({
      name: '',
      purchasePrice: '',
      salePrice: '',
      quantity: '',
      barcode: '',
      notes: ''
    });
    setAddDialog(true);
  };

  const handleEdit = (part) => {
    setSelectedPart(part);
    setEditData({ ...part });
    setEditDialog(true);
  };

  const handleDelete = (part) => {
    setSelectedPart(part);
    setDeleteDialog(true);
  };

  const handleExport = () => {
    const columns = [
      { field: 'name', header: 'اسم القطعة', width: 60 },
      { field: 'purchasePrice', header: 'سعر الشراء', width: 40 },
      { field: 'salePrice', header: 'سعر البيع', width: 40 },
      { field: 'quantity', header: 'الكمية', width: 30 },
      { field: 'barcode', header: 'الباركود', width: 50 },
      { field: 'notes', header: 'ملاحظات', width: 60 }
    ];
    
    exportToPDF(spareParts, 'قطع الغيار', columns);
  };

  const confirmAdd = () => {
    if (!newPart.name || !newPart.purchasePrice || !newPart.salePrice || !newPart.quantity) {
      setAlert({
        open: true,
        message: 'يرجى ملء جميع الحقول المطلوبة',
        severity: 'error'
      });
      return;
    }

    const partData = {
      ...newPart,
      purchasePrice: parseFloat(newPart.purchasePrice),
      salePrice: parseFloat(newPart.salePrice),
      quantity: parseInt(newPart.quantity),
      barcode: newPart.barcode || `SP${Date.now()}`
    };

    addSparePart(partData);
    setAddDialog(false);
    setAlert({
      open: true,
      message: 'تم إضافة قطعة الغيار بنجاح',
      severity: 'success'
    });
  };

  const confirmEdit = () => {
    const updatedData = {
      ...editData,
      purchasePrice: parseFloat(editData.purchasePrice),
      salePrice: parseFloat(editData.salePrice),
      quantity: parseInt(editData.quantity)
    };

    updateSparePart(selectedPart.id, updatedData);
    setEditDialog(false);
    setAlert({
      open: true,
      message: 'تم تحديث قطعة الغيار بنجاح',
      severity: 'success'
    });
  };

  const confirmDelete = () => {
    deleteSparePart(selectedPart.id);
    setDeleteDialog(false);
    setAlert({
      open: true,
      message: 'تم حذف قطعة الغيار بنجاح',
      severity: 'success'
    });
  };

  const columns = [
    {
      field: 'name',
      headerName: 'اسم القطعة',
      width: 200
    },
    {
      field: 'purchasePrice',
      headerName: 'سعر الشراء',
      width: 120,
      renderCell: (params) => `${params.value} ريال`
    },
    {
      field: 'salePrice',
      headerName: 'سعر البيع',
      width: 120,
      renderCell: (params) => `${params.value} ريال`
    },
    {
      field: 'quantity',
      headerName: 'الكمية',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={params.value < 5 ? 'error' : params.value < 10 ? 'warning' : 'success'}
          variant="filled"
          size="small"
        />
      )
    },
    {
      field: 'barcode',
      headerName: 'الباركود',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value}
          variant="outlined"
          size="small"
        />
      )
    },
    {
      field: 'profit',
      headerName: 'الربح',
      width: 100,
      renderCell: (params) => {
        const profit = params.row.salePrice - params.row.purchasePrice;
        return (
          <Chip
            label={`${profit} ريال`}
            color={profit > 0 ? 'success' : 'error'}
            variant="filled"
            size="small"
          />
        );
      }
    },
    {
      field: 'createdAt',
      headerName: 'تاريخ الإضافة',
      width: 150,
      renderCell: (params) => new Date(params.value).toLocaleDateString('ar')
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <Tooltip title="تعديل">
            <IconButton
              size="small"
              onClick={() => handleEdit(params.row)}
              color="info"
            >
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="طباعة">
            <IconButton
              size="small"
              onClick={() => {/* Handle print individual part */}}
              color="success"
            >
              <PrintIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="حذف">
            <IconButton
              size="small"
              onClick={() => handleDelete(params.row)}
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      )
    }
  ];

  return (
    <Layout>
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            إدارة قطع الغيار
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<ImportIcon />}
              onClick={() => {/* Handle import */}}
            >
              استيراد Excel
            </Button>
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleExport}
            >
              تصدير PDF
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAdd}
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
              }}
            >
              إضافة قطعة غيار
            </Button>
          </Box>
        </Box>

        <Card>
          <CardContent>
            <Box sx={{ height: 600, width: '100%' }}>
              <DataGrid
                rows={spareParts}
                columns={columns}
                pageSize={10}
                rowsPerPageOptions={[10, 25, 50]}
                disableSelectionOnClick
                sx={{
                  '& .MuiDataGrid-row:hover': {
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                  },
                }}
              />
            </Box>
          </CardContent>
        </Card>

        {/* Add Part Dialog */}
        <Dialog open={addDialog} onClose={() => setAddDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>إضافة قطعة غيار جديدة</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="اسم القطعة *"
                  value={newPart.name}
                  onChange={(e) => setNewPart(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="الباركود"
                  value={newPart.barcode}
                  onChange={(e) => setNewPart(prev => ({ ...prev, barcode: e.target.value }))}
                  placeholder="سيتم إنشاؤه تلقائياً إذا ترك فارغاً"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="سعر الشراء *"
                  type="number"
                  value={newPart.purchasePrice}
                  onChange={(e) => setNewPart(prev => ({ ...prev, purchasePrice: e.target.value }))}
                  InputProps={{
                    endAdornment: 'ريال'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="سعر البيع *"
                  type="number"
                  value={newPart.salePrice}
                  onChange={(e) => setNewPart(prev => ({ ...prev, salePrice: e.target.value }))}
                  InputProps={{
                    endAdornment: 'ريال'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="الكمية *"
                  type="number"
                  value={newPart.quantity}
                  onChange={(e) => setNewPart(prev => ({ ...prev, quantity: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={3}
                  value={newPart.notes}
                  onChange={(e) => setNewPart(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAddDialog(false)}>إلغاء</Button>
            <Button onClick={confirmAdd} variant="contained">إضافة القطعة</Button>
          </DialogActions>
        </Dialog>

        {/* Edit Part Dialog */}
        <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>تعديل قطعة الغيار</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="اسم القطعة"
                  value={editData.name || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="الباركود"
                  value={editData.barcode || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, barcode: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="سعر الشراء"
                  type="number"
                  value={editData.purchasePrice || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, purchasePrice: e.target.value }))}
                  InputProps={{
                    endAdornment: 'ريال'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="سعر البيع"
                  type="number"
                  value={editData.salePrice || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, salePrice: e.target.value }))}
                  InputProps={{
                    endAdornment: 'ريال'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="الكمية"
                  type="number"
                  value={editData.quantity || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, quantity: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={3}
                  value={editData.notes || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEditDialog(false)}>إلغاء</Button>
            <Button onClick={confirmEdit} variant="contained">حفظ التغييرات</Button>
          </DialogActions>
        </Dialog>

        {/* Delete Dialog */}
        <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
          <DialogTitle>تأكيد الحذف</DialogTitle>
          <DialogContent>
            <Typography>
              هل أنت متأكد من حذف قطعة الغيار "{selectedPart?.name}"؟
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialog(false)}>إلغاء</Button>
            <Button onClick={confirmDelete} variant="contained" color="error">حذف</Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setAlert(prev => ({ ...prev, open: false }))}
            severity={alert.severity}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default SpareParts;
