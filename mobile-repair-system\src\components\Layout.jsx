import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Paper,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Add as AddIcon,
  List as ListIcon,
  People as PeopleIcon,
  Build as BuildIcon,
  Description as DescriptionIcon,
  Assessment as AssessmentIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Search as SearchIcon,
  AccountCircle,
  PhoneAndroid as PhoneIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useData } from '../context/DataContext';

const drawerWidth = 280;

const menuItems = [
  {
    text: 'لوحة التحكم الرئيسية',
    icon: <DashboardIcon />,
    path: '/',
    color: '#1976D2',
    description: 'نظرة عامة على النظام'
  },
  {
    text: 'إضافة طلب صيانة',
    icon: <AddIcon />,
    path: '/add-repair',
    color: '#2E7D32',
    description: 'إضافة طلب صيانة جديد'
  },
  {
    text: 'إدارة الطلبات',
    icon: <ListIcon />,
    path: '/view-repairs',
    color: '#F57C00',
    description: 'عرض وإدارة جميع الطلبات'
  },
  {
    text: 'إدارة العملاء',
    icon: <PeopleIcon />,
    path: '/customers',
    color: '#7B1FA2',
    description: 'قاعدة بيانات العملاء'
  },
  {
    text: 'مخزن قطع الغيار',
    icon: <BuildIcon />,
    path: '/spare-parts',
    color: '#5D4037',
    description: 'إدارة المخزون'
  },
  {
    text: 'المستندات والإيصالات',
    icon: <DescriptionIcon />,
    path: '/documents',
    color: '#455A64',
    description: 'أرشيف المستندات'
  },
  {
    text: 'التقارير والإحصائيات',
    icon: <AssessmentIcon />,
    path: '/reports',
    color: '#E91E63',
    description: 'تقارير مفصلة'
  },
  {
    text: 'سجل العمليات',
    icon: <HistoryIcon />,
    path: '/activity-log',
    color: '#FF5722',
    description: 'تتبع جميع العمليات'
  },
  {
    text: 'إعدادات النظام',
    icon: <SettingsIcon />,
    path: '/settings',
    color: '#607D8B',
    description: 'تخصيص النظام'
  },
];

const Layout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();
  const { searchAll, settings } = useData();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleMenuClose();
    navigate('/login');
  };

  const handleSearch = (query) => {
    setSearchQuery(query);
    if (query.trim()) {
      const results = searchAll(query);
      setSearchResults(results);
      setShowSearchResults(true);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  };

  const handleSearchResultClick = (result) => {
    setShowSearchResults(false);
    setSearchQuery('');
    
    switch (result.type) {
      case 'repair':
        navigate('/view-repairs', { state: { highlightId: result.data.id } });
        break;
      case 'customer':
        navigate('/customers', { state: { highlightId: result.data.id } });
        break;
      case 'sparePart':
        navigate('/spare-parts', { state: { highlightId: result.data.id } });
        break;
      default:
        break;
    }
  };

  const getResultTypeLabel = (type) => {
    switch (type) {
      case 'repair': return 'طلب صيانة';
      case 'customer': return 'عميل';
      case 'sparePart': return 'قطعة غيار';
      default: return '';
    }
  };

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%)',
          color: 'white',
          padding: 4,
          textAlign: 'center',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #D32F2F 0%, #F44336 50%, #D32F2F 100%)',
          }
        }}
      >
        <Avatar
          sx={{
            width: 80,
            height: 80,
            margin: '0 auto 20px',
            background: 'rgba(255, 255, 255, 0.15)',
            border: '3px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
          }}
        >
          <PhoneIcon sx={{ fontSize: 40, color: 'white' }} />
        </Avatar>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {settings.centerName || 'مركز الرافدين للصيانة'}
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.85rem' }}>
          نظام إدارة شامل ومتطور
        </Typography>
        <Typography variant="caption" sx={{ opacity: 0.8, display: 'block', mt: 1 }}>
          🇮🇶 صنع في العراق
        </Typography>
      </Box>

      <Divider sx={{ borderColor: 'rgba(0,0,0,0.1)' }} />

      <List sx={{ padding: 2, flexGrow: 1 }}>
        {menuItems.map((item, index) => (
          <ListItem key={item.text} disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              onClick={() => navigate(item.path)}
              selected={location.pathname === item.path}
              sx={{
                borderRadius: 3,
                padding: '12px 16px',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&.Mui-selected': {
                  background: `linear-gradient(135deg, ${item.color}15 0%, ${item.color}25 100%)`,
                  color: item.color,
                  border: `2px solid ${item.color}30`,
                  transform: 'translateX(8px)',
                  boxShadow: `0 4px 20px ${item.color}20`,
                  '&:hover': {
                    background: `linear-gradient(135deg, ${item.color}20 0%, ${item.color}35 100%)`,
                    transform: 'translateX(12px)',
                  },
                },
                '&:hover': {
                  backgroundColor: `${item.color}10`,
                  transform: 'translateX(4px)',
                  boxShadow: `0 2px 12px ${item.color}15`,
                },
              }}
            >
              <ListItemIcon
                sx={{
                  color: location.pathname === item.path ? item.color : `${item.color}80`,
                  minWidth: 45,
                  '& svg': {
                    fontSize: '1.4rem',
                  },
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                secondary={item.description}
                sx={{
                  '& .MuiListItemText-primary': {
                    fontWeight: location.pathname === item.path ? 'bold' : '500',
                    fontSize: '0.95rem',
                    color: location.pathname === item.path ? item.color : 'text.primary',
                  },
                  '& .MuiListItemText-secondary': {
                    fontSize: '0.75rem',
                    opacity: 0.7,
                    color: location.pathname === item.path ? item.color : 'text.secondary',
                  },
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      {/* Footer */}
      <Box sx={{ p: 2, textAlign: 'center', borderTop: '1px solid rgba(0,0,0,0.1)' }}>
        <Typography variant="caption" color="text.secondary">
          الإصدار 1.0.0 - 2024
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
          background: 'linear-gradient(135deg, #1B5E20 0%, #2E7D32 50%, #4CAF50 100%)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
          borderBottom: '3px solid #D32F2F',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Box sx={{ flexGrow: 1, position: 'relative' }}>
            <TextField
              size="medium"
              placeholder="🔍 البحث الذكي في جميع أجزاء النظام..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon sx={{ color: 'rgba(255, 255, 255, 0.9)' }} />
                  </InputAdornment>
                ),
                sx: {
                  color: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  borderRadius: 3,
                  backdropFilter: 'blur(10px)',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    borderWidth: 2,
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'rgba(255, 255, 255, 0.6)',
                    borderWidth: 2,
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'white',
                    borderWidth: 2,
                    boxShadow: '0 0 0 3px rgba(255, 255, 255, 0.1)',
                  },
                },
              }}
              sx={{
                maxWidth: 500,
                '& .MuiInputBase-input': {
                  padding: '12px 14px',
                  fontSize: '1rem',
                  fontWeight: 500,
                },
                '& .MuiInputBase-input::placeholder': {
                  color: 'rgba(255, 255, 255, 0.8)',
                  opacity: 1,
                  fontWeight: 400,
                },
              }}
            />

            {showSearchResults && searchResults.length > 0 && (
              <Paper
                sx={{
                  position: 'absolute',
                  top: '100%',
                  left: 0,
                  right: 0,
                  maxWidth: 400,
                  maxHeight: 300,
                  overflow: 'auto',
                  zIndex: 1000,
                  mt: 1,
                }}
              >
                {searchResults.map((result, index) => (
                  <Box
                    key={index}
                    onClick={() => handleSearchResultClick(result)}
                    sx={{
                      p: 2,
                      cursor: 'pointer',
                      '&:hover': { backgroundColor: 'grey.100' },
                      borderBottom: '1px solid',
                      borderColor: 'grey.200',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={getResultTypeLabel(result.type)}
                        size="small"
                        color="primary"
                      />
                      <Typography variant="body2">
                        {result.type === 'repair' && result.data.customerName}
                        {result.type === 'customer' && result.data.name}
                        {result.type === 'sparePart' && result.data.name}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Paper>
            )}
          </Box>

          <IconButton
            size="large"
            edge="end"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenuClick}
            color="inherit"
          >
            <AccountCircle />
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>
              <ListItemIcon>
                <AccountCircle fontSize="small" />
              </ListItemIcon>
              {user?.username}
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          mt: 8,
          backgroundColor: '#f5f5f5',
          minHeight: 'calc(100vh - 64px)',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default Layout;
