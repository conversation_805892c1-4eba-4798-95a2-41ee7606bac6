import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Add as AddIcon, Print as PrintIcon } from '@mui/icons-material';
import dayjs from 'dayjs';
import Layout from '../components/Layout';
import { useData } from '../context/DataContext';
import { generateRepairReceipt } from '../utils/pdfGenerator';

const AddRepair = () => {
  const { customers, phoneManufacturers, addRepair, addCustomer } = useData();
  
  const [formData, setFormData] = useState({
    customerName: '',
    customerPhone: '',
    phoneType: '',
    problem: '',
    price: '',
    paymentType: 'cash',
    partialAmount: '',
    date: dayjs(),
    notes: ''
  });

  const [newCustomer, setNewCustomer] = useState({
    name: '',
    phone: '',
    address: '',
    socialMedia: '',
    notes: ''
  });

  const [showNewCustomerDialog, setShowNewCustomerDialog] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'success' });
  const [lastCreatedRepair, setLastCreatedRepair] = useState(null);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCustomerSelect = (customer) => {
    setSelectedCustomer(customer);
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerName: customer.name,
        customerPhone: customer.phone
      }));
    }
  };

  const handleAddNewCustomer = () => {
    if (!newCustomer.name || !newCustomer.phone) {
      setAlert({
        open: true,
        message: 'يرجى إدخال اسم العميل ورقم الهاتف على الأقل',
        severity: 'error'
      });
      return;
    }

    const customer = addCustomer(newCustomer);
    handleCustomerSelect(customer);
    setShowNewCustomerDialog(false);
    setNewCustomer({
      name: '',
      phone: '',
      address: '',
      socialMedia: '',
      notes: ''
    });
    setAlert({
      open: true,
      message: 'تم إضافة العميل بنجاح',
      severity: 'success'
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validation
    if (!formData.customerName || !formData.customerPhone || !formData.phoneType || 
        !formData.problem || !formData.price) {
      setAlert({
        open: true,
        message: 'يرجى ملء جميع الحقول المطلوبة',
        severity: 'error'
      });
      return;
    }

    if (formData.paymentType === 'partial' && !formData.partialAmount) {
      setAlert({
        open: true,
        message: 'يرجى إدخال المبلغ المدفوع جزئياً',
        severity: 'error'
      });
      return;
    }

    // Create repair
    const repairData = {
      customerName: formData.customerName,
      customerPhone: formData.customerPhone,
      phoneType: formData.phoneType,
      problem: formData.problem,
      price: parseFloat(formData.price),
      paymentType: formData.paymentType,
      partialAmount: formData.paymentType === 'partial' ? parseFloat(formData.partialAmount) : 0,
      date: formData.date.toISOString(),
      notes: formData.notes,
      customerId: selectedCustomer?.id
    };

    const newRepair = addRepair(repairData);
    setLastCreatedRepair(newRepair);

    // Reset form
    setFormData({
      customerName: '',
      customerPhone: '',
      phoneType: '',
      problem: '',
      price: '',
      paymentType: 'cash',
      partialAmount: '',
      date: dayjs(),
      notes: ''
    });
    setSelectedCustomer(null);

    setAlert({
      open: true,
      message: 'تم إضافة طلب الصيانة بنجاح',
      severity: 'success'
    });
  };

  const handlePrintReceipt = () => {
    if (lastCreatedRepair) {
      generateRepairReceipt(lastCreatedRepair);
    }
  };

  return (
    <Layout>
      <Box>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" color="primary">
          إضافة طلب صيانة جديد
        </Typography>

        <Card sx={{ maxWidth: 800, mx: 'auto' }}>
          <CardContent sx={{ p: 4 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Customer Selection */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-end' }}>
                    <Autocomplete
                      fullWidth
                      options={customers}
                      getOptionLabel={(option) => `${option.name} - ${option.phone}`}
                      value={selectedCustomer}
                      onChange={(event, newValue) => handleCustomerSelect(newValue)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="اختر عميل موجود"
                          variant="outlined"
                        />
                      )}
                    />
                    <Button
                      variant="outlined"
                      onClick={() => setShowNewCustomerDialog(true)}
                      sx={{ minWidth: 120, height: 56 }}
                    >
                      عميل جديد
                    </Button>
                  </Box>
                </Grid>

                {/* Customer Name */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="اسم العميل *"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    required
                  />
                </Grid>

                {/* Customer Phone */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="رقم الهاتف *"
                    value={formData.customerPhone}
                    onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                    required
                  />
                </Grid>

                {/* Phone Type */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>نوع الهاتف *</InputLabel>
                    <Select
                      value={formData.phoneType}
                      onChange={(e) => handleInputChange('phoneType', e.target.value)}
                      label="نوع الهاتف *"
                    >
                      {phoneManufacturers.map((manufacturer) => (
                        <MenuItem key={manufacturer} value={manufacturer}>
                          {manufacturer}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                {/* Date */}
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="التاريخ"
                    value={formData.date}
                    onChange={(newValue) => handleInputChange('date', newValue)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>

                {/* Problem */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="المشكلة (العطل) *"
                    multiline
                    rows={3}
                    value={formData.problem}
                    onChange={(e) => handleInputChange('problem', e.target.value)}
                    required
                  />
                </Grid>

                {/* Price */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="السعر *"
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    InputProps={{
                      endAdornment: 'ريال'
                    }}
                    required
                  />
                </Grid>

                {/* Payment Type */}
                <Grid item xs={12} md={6}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend">طريقة الدفع</FormLabel>
                    <RadioGroup
                      value={formData.paymentType}
                      onChange={(e) => handleInputChange('paymentType', e.target.value)}
                    >
                      <FormControlLabel value="cash" control={<Radio />} label="نقداً" />
                      <FormControlLabel value="partial" control={<Radio />} label="جزء منه" />
                      <FormControlLabel value="deferred" control={<Radio />} label="آجل بعد الصيانة" />
                    </RadioGroup>
                  </FormControl>
                </Grid>

                {/* Partial Amount */}
                {formData.paymentType === 'partial' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="المبلغ المدفوع"
                      type="number"
                      value={formData.partialAmount}
                      onChange={(e) => handleInputChange('partialAmount', e.target.value)}
                      InputProps={{
                        endAdornment: 'ريال'
                      }}
                    />
                  </Grid>
                )}

                {/* Notes */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="ملاحظات"
                    multiline
                    rows={2}
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                  />
                </Grid>

                {/* Submit Button */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      startIcon={<AddIcon />}
                      sx={{
                        minWidth: 200,
                        background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                      }}
                    >
                      إضافة طلب الصيانة
                    </Button>
                    
                    {lastCreatedRepair && (
                      <Button
                        variant="outlined"
                        size="large"
                        startIcon={<PrintIcon />}
                        onClick={handlePrintReceipt}
                        sx={{ minWidth: 150 }}
                      >
                        طباعة الإيصال
                      </Button>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>

        {/* New Customer Dialog */}
        <Dialog open={showNewCustomerDialog} onClose={() => setShowNewCustomerDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>إضافة عميل جديد</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="الاسم الثلاثي *"
                  value={newCustomer.name}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="رقم الهاتف *"
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, phone: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="العنوان"
                  value={newCustomer.address}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, address: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="برامج التواصل"
                  value={newCustomer.socialMedia}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, socialMedia: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="ملاحظات"
                  multiline
                  rows={2}
                  value={newCustomer.notes}
                  onChange={(e) => setNewCustomer(prev => ({ ...prev, notes: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowNewCustomerDialog(false)}>إلغاء</Button>
            <Button onClick={handleAddNewCustomer} variant="contained">إضافة العميل</Button>
          </DialogActions>
        </Dialog>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert(prev => ({ ...prev, open: false }))}
        >
          <Alert
            onClose={() => setAlert(prev => ({ ...prev, open: false }))}
            severity={alert.severity}
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Box>
    </Layout>
  );
};

export default AddRepair;
